# JSA Generator Demo Recording Guide

## 🎬 Recording Setup

### Tools Recommended:
- **OBS Studio** (Free, professional quality)
- **Loom** (Easy web-based recording)
- **Windows Game Bar** (Win + G, built-in)

### Recording Settings:
- **Resolution**: 1920x1080 (Full HD)
- **Frame Rate**: 30 FPS
- **Duration**: 3-5 minutes total
- **Audio**: Include voice narration (optional but recommended)

## 📝 Demo Script Structure

### 1. Introduction (30 seconds)
**Show**: Application homepage/login screen
**Say**: "Welcome to the JSA Generator - an AI-powered tool that creates comprehensive Job Safety Analyses in seconds."

### 2. JSA Generation Demo (90 seconds)
**Show**: 
- Enter activity: "Installing electrical panels in a construction site"
- Select Ollama model
- Click "Generate JSA"
- Show the generated JSA with steps, hazards, and controls

**Say**: "Simply describe your work activity, select an AI model, and watch as a complete JSA is generated automatically with detailed safety steps and hazard controls."

### 3. Management Features (60 seconds)
**Show**:
- Dashboard with saved JSAs
- Edit a JSA
- Save as template
- Export options (PDF, Word, Excel)

**Say**: "Manage all your JSAs in one place - edit, save as templates, and export in multiple formats for easy sharing."

### 4. Translation Feature (45 seconds)
**Show**:
- Language selector
- Translate a JSA to Albanian/English
- Show the translated result

**Say**: "Support for multiple languages ensures your safety documentation works for diverse teams and international operations."

### 5. Templates Demo (30 seconds)
**Show**:
- Create template from existing JSA
- Use template to generate new JSA
- Public/private template options

**Say**: "Create reusable templates for common activities to save time and ensure consistency across your organization."

### 6. Conclusion (15 seconds)
**Show**: Final dashboard view
**Say**: "The JSA Generator - making workplace safety documentation faster, smarter, and more accessible."

## 🎯 Key Features to Highlight

### Must Show:
1. ✅ **AI Generation**: The core feature - input → AI processing → output
2. ✅ **Export Options**: PDF, Word, Excel downloads
3. ✅ **Multilingual**: Language switching and translation
4. ✅ **Templates**: Creating and using templates
5. ✅ **Management**: Dashboard, editing, organizing

### Nice to Show:
- User authentication/login
- Different JSA examples
- Search/filter functionality
- Settings/preferences

## 📋 Pre-Recording Checklist

### Prepare Your Environment:
- [ ] Clean desktop background
- [ ] Close unnecessary applications
- [ ] Prepare sample data (activities to demonstrate)
- [ ] Test your microphone (if recording audio)
- [ ] Have a glass of water nearby

### Prepare Sample Activities:
1. "Installing electrical panels in a construction site"
2. "Operating a forklift in a warehouse"
3. "Welding steel beams at height"
4. "Chemical mixing in laboratory"

### Browser Setup:
- [ ] Clear browser cache
- [ ] Use incognito/private mode for clean recording
- [ ] Zoom level at 100%
- [ ] Hide bookmarks bar

## 🎨 Recording Tips

### Visual:
- **Slow mouse movements** - viewers need to follow
- **Pause briefly** after each click
- **Highlight important areas** with cursor
- **Use consistent timing** between actions

### Audio (if included):
- **Speak clearly** and at moderate pace
- **Explain what you're doing** as you do it
- **Use professional tone** but keep it conversational
- **Pause between sections** for editing

### Technical:
- **Record in segments** if needed (easier to edit)
- **Keep consistent window size** throughout
- **Avoid scrolling too fast**
- **Show loading states** briefly (shows it's working)

## 🔄 Post-Recording

### Video Editing:
- **Trim dead time** at beginning/end
- **Add title screen** with app name
- **Include captions** if possible
- **Add smooth transitions** between sections
- **Export in web-friendly format** (MP4, H.264)

### File Optimization:
- **Target file size**: Under 50MB for web
- **Compression**: Balance quality vs. file size
- **Format**: MP4 for maximum compatibility

## 📤 Integration Options

### Option 1: Self-Hosted (Recommended)
- Upload to `/public/videos/jsa-demo.mp4`
- Update ServicesPage.tsx to include video element
- Best performance and control

### Option 2: YouTube/Vimeo
- Upload to platform
- Get embed code
- Update ServicesPage.tsx with iframe
- Easier management, external hosting

### Option 3: Multiple Short Clips
- Create separate videos for each feature
- Show in different sections
- More engaging, easier to digest

## 🎯 Success Metrics

A good demo video should:
- ✅ Show the complete workflow in under 5 minutes
- ✅ Demonstrate clear value proposition
- ✅ Be easy to follow for non-technical users
- ✅ Highlight unique features (AI, multilingual, templates)
- ✅ End with clear next steps (contact/demo request)

---

**Ready to record? Follow this guide and you'll have a professional demo that showcases your JSA Generator effectively!** 🚀
