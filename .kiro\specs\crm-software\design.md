# Design Document

## Overview

This design document outlines the architecture for transforming the existing SafeWork-Kosova website into a Kosovo law-compliant Customer Relationship Management (CRM) platform. The system will maintain the current React/TypeScript/Vite tech stack while adding CRM functionality that focuses on educational resource distribution, user inquiry management, and compliant service provider guidance.

The transformation will convert the existing consulting-focused website into an educational platform that provides workplace safety resources, manages user inquiries, and guides users to qualified professionals while maintaining full compliance with Kosovo law.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React SPA] --> B[Educational Resources]
        A --> C[User Portal]
        A --> D[Inquiry Management]
        A --> E[Resource Library]
    end
    
    subgraph "API Layer"
        F[Express.js API] --> G[Authentication]
        F --> H[User Management]
        F --> I[Resource Management]
        F --> J[Inquiry Processing]
        F --> K[Analytics]
    end
    
    subgraph "Data Layer"
        L[(SQLite/PostgreSQL)] --> M[Users]
        L --> N[Inquiries]
        L --> O[Resources]
        L --> P[Analytics]
    end
    
    subgraph "External Services"
        Q[Email Service - Resend]
        R[File Storage]
        S[Analytics Service]
    end
    
    A --> F
    F --> L
    F --> Q
    F --> R
    F --> S
```

### Technology Stack

**Frontend:**
- React 18.3.1 with TypeScript
- Vite for build tooling
- React Router for navigation
- Tailwind CSS for styling
- i18next for internationalization (Albanian/English)
- React Helmet Async for SEO

**Backend:**
- Express.js 4.21.2 with TypeScript
- SQLite for development, PostgreSQL for production
- Resend for email services
- Express Rate Limiting for API protection
- Helmet for security headers
- CORS for cross-origin requests

**Infrastructure:**
- Vercel for hosting and deployment
- File storage for educational resources
- Environment-based configuration

## Components and Interfaces

### Frontend Components

#### 1. Educational Resource Components
```typescript
interface ResourceLibrary {
  categories: ResourceCategory[];
  searchFilters: FilterOptions;
  downloadTracking: AnalyticsEvent[];
}

interface ResourceCategory {
  id: string;
  name: string;
  description: string;
  resources: Resource[];
  disclaimer: string; // Legal disclaimer for educational use
}

interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'template' | 'guide' | 'checklist' | 'video' | 'document';
  downloadUrl: string;
  previewUrl?: string;
  tags: string[];
  lastUpdated: Date;
  downloadCount: number;
}
```

#### 2. User Inquiry Components
```typescript
interface InquiryForm {
  userInfo: UserContactInfo;
  inquiryType: InquiryType;
  urgencyLevel: 'low' | 'medium' | 'high' | 'emergency';
  description: string;
  companyInfo?: CompanyInfo;
  preferredContact: 'email'; // Only email contact to minimize data collection
}

interface InquiryType {
  category: 'risk-assessment' | 'training' | 'compliance' | 'emergency' | 'general';
  subcategory: string;
  requiresProfessional: boolean;
}
```

#### 3. Service Provider Guidance Components
```typescript
interface ServiceProviderGuidance {
  qualificationCriteria: string[];
  verificationSteps: string[];
  regulatoryBodies: RegulatoryBody[];
  disclaimer: string;
}

interface RegulatoryBody {
  name: string;
  website: string;
  contactInfo: ContactInfo;
  jurisdiction: string;
}
```

### Backend API Interfaces

#### 1. User Management API
```typescript
interface UserAPI {
  POST /api/users/inquiry: (inquiry: InquiryForm) => Promise<InquiryResponse>;
  GET /api/users/resources: (filters: ResourceFilters) => Promise<Resource[]>;
  POST /api/users/download: (resourceId: string) => Promise<DownloadResponse>;
  GET /api/users/guidance: (category: string) => Promise<ServiceProviderGuidance>;
}
```

#### 2. Admin Management API
```typescript
interface AdminAPI {
  GET /api/admin/inquiries: (filters: InquiryFilters) => Promise<Inquiry[]>;
  PUT /api/admin/inquiries/:id: (update: InquiryUpdate) => Promise<Inquiry>;
  GET /api/admin/analytics: (dateRange: DateRange) => Promise<AnalyticsData>;
  POST /api/admin/resources: (resource: ResourceCreate) => Promise<Resource>;
  PUT /api/admin/resources/:id: (update: ResourceUpdate) => Promise<Resource>;
}
```

#### 3. Compliance API
```typescript
interface ComplianceAPI {
  GET /api/compliance/disclaimers: () => Promise<Disclaimer[]>;
  POST /api/compliance/audit-log: (event: AuditEvent) => Promise<void>;
  GET /api/compliance/privacy-policy: () => Promise<PrivacyPolicy>;
  POST /api/compliance/data-request: (request: DataRequest) => Promise<void>;
}
```

## Data Models

### Core Data Models

#### 1. User Inquiry Model
```typescript
interface Inquiry {
  id: string;
  userId?: string; // Optional for anonymous inquiries
  contactInfo: {
    name: string;
    email: string;
    company?: string; // Only company name, no address details
  };
  inquiryType: {
    category: InquiryCategory;
    subcategory: string;
    urgencyLevel: UrgencyLevel;
  };
  description: string;
  status: 'new' | 'in-progress' | 'responded' | 'closed' | 'referred';
  assignedTo?: string;
  createdAt: Date;
  updatedAt: Date;
  responseHistory: InquiryResponse[];
  tags: string[];
  requiresProfessional: boolean;
}
```

#### 2. Resource Model
```typescript
interface Resource {
  id: string;
  title: string;
  description: string;
  category: ResourceCategory;
  type: ResourceType;
  content: {
    fileUrl: string;
    previewUrl?: string;
    thumbnailUrl?: string;
  };
  metadata: {
    fileSize: number;
    format: string;
    language: 'sq' | 'en';
    version: string;
  };
  analytics: {
    downloadCount: number;
    viewCount: number;
    lastAccessed: Date;
  };
  compliance: {
    disclaimer: string;
    lastReviewed: Date;
    reviewedBy: string;
  };
  createdAt: Date;
  updatedAt: Date;
}
```

#### 3. User Activity Model
```typescript
interface UserActivity {
  id: string;
  sessionId: string;
  userInfo?: {
    email?: string;
    ipAddress: string;
    userAgent: string;
  };
  activity: {
    type: 'resource_download' | 'inquiry_submit' | 'page_view' | 'search';
    resourceId?: string;
    inquiryId?: string;
    searchQuery?: string;
    pageUrl?: string;
  };
  timestamp: Date;
  metadata: Record<string, any>;
}
```

### Database Schema

#### SQLite Schema (Development)
```sql
-- Users table for inquiry tracking
CREATE TABLE inquiries (
  id TEXT PRIMARY KEY,
  contact_name TEXT NOT NULL,
  contact_email TEXT NOT NULL,
  company_name TEXT, -- Only company name, no phone or address
  inquiry_category TEXT NOT NULL,
  inquiry_subcategory TEXT,
  urgency_level TEXT NOT NULL,
  description TEXT NOT NULL,
  status TEXT DEFAULT 'new',
  assigned_to TEXT,
  requires_professional BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Resources table
CREATE TABLE resources (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  preview_url TEXT,
  file_size INTEGER,
  format TEXT,
  language TEXT DEFAULT 'sq',
  download_count INTEGER DEFAULT 0,
  disclaimer TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- User activities for analytics
CREATE TABLE user_activities (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  user_email TEXT,
  ip_address TEXT,
  activity_type TEXT NOT NULL,
  resource_id TEXT,
  inquiry_id TEXT,
  metadata TEXT, -- JSON
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Error Handling

### Frontend Error Handling

#### 1. User-Facing Errors
```typescript
interface ErrorHandler {
  handleResourceDownloadError: (error: Error) => void;
  handleInquirySubmissionError: (error: Error) => void;
  handleNetworkError: (error: Error) => void;
  displayUserFriendlyMessage: (errorType: ErrorType) => string;
}

// Error boundaries for React components
class ResourceErrorBoundary extends React.Component {
  // Handle resource loading failures gracefully
}

class InquiryFormErrorBoundary extends React.Component {
  // Handle form submission failures with retry options
}
```

#### 2. Compliance Error Handling
```typescript
interface ComplianceErrorHandler {
  handleDataProtectionViolation: (violation: DataViolation) => void;
  handleUnauthorizedAccess: (attempt: AccessAttempt) => void;
  logComplianceEvent: (event: ComplianceEvent) => void;
  notifyAdministrators: (alert: ComplianceAlert) => void;
}
```

### Backend Error Handling

#### 1. API Error Responses
```typescript
interface APIErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: Date;
    requestId: string;
  };
  success: false;
}

// Standard error codes
enum ErrorCodes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}
```

#### 2. Audit Logging
```typescript
interface AuditLogger {
  logDataAccess: (userId: string, resourceId: string) => void;
  logInquirySubmission: (inquiry: Inquiry) => void;
  logComplianceEvent: (event: ComplianceEvent) => void;
  logSecurityEvent: (event: SecurityEvent) => void;
}
```

## Testing Strategy

### Frontend Testing

#### 1. Component Testing
```typescript
// Resource download component tests
describe('ResourceDownload', () => {
  test('displays compliance disclaimer before download', () => {});
  test('tracks download analytics', () => {});
  test('handles download failures gracefully', () => {});
});

// Inquiry form tests
describe('InquiryForm', () => {
  test('validates required fields', () => {});
  test('shows appropriate service provider guidance', () => {});
  test('handles submission errors', () => {});
});
```

#### 2. Integration Testing
```typescript
// End-to-end user flows
describe('User Journey', () => {
  test('user can browse resources and download templates', () => {});
  test('user can submit inquiry and receive guidance', () => {});
  test('user sees appropriate disclaimers throughout', () => {});
});
```

### Backend Testing

#### 1. API Testing
```typescript
// Inquiry API tests
describe('Inquiry API', () => {
  test('POST /api/users/inquiry creates inquiry with compliance logging', () => {});
  test('validates inquiry data and sanitizes input', () => {});
  test('sends appropriate email notifications', () => {});
});

// Resource API tests
describe('Resource API', () => {
  test('GET /api/users/resources returns filtered resources', () => {});
  test('POST /api/users/download tracks analytics', () => {});
  test('enforces rate limiting on downloads', () => {});
});
```

#### 2. Compliance Testing
```typescript
// Data protection tests
describe('Data Protection', () => {
  test('personal data is properly encrypted', () => {});
  test('audit logs are created for data access', () => {});
  test('data retention policies are enforced', () => {});
});

// Legal compliance tests
describe('Legal Compliance', () => {
  test('disclaimers are displayed appropriately', () => {});
  test('service provider guidance is accurate', () => {});
  test('no consulting services are offered', () => {});
});
```

### Performance Testing

#### 1. Load Testing
- Resource download performance under concurrent users
- Inquiry form submission handling
- Database query optimization
- API response times

#### 2. Security Testing
- Input validation and sanitization
- Rate limiting effectiveness
- Authentication and authorization
- Data encryption verification

## Migration Strategy

### Phase 1: Infrastructure Setup
1. Set up new database schema alongside existing system
2. Implement new API endpoints for CRM functionality
3. Create admin interface for managing inquiries and resources
4. Set up compliance logging and audit trails

### Phase 2: Content Transformation
1. Convert existing service pages to educational resource categories
2. Transform service CTAs to resource download and guidance CTAs
3. Remove phone numbers and street addresses from all contact forms and displays
4. Add compliance disclaimers throughout the platform
5. Create service provider guidance content

### Phase 3: User Experience Updates
1. Update navigation to reflect educational focus
2. Implement inquiry form with appropriate routing
3. Add resource library with search and filtering
4. Update contact forms to handle different inquiry types

### Phase 4: Compliance Implementation
1. Add legal disclaimers and privacy policies
2. Implement data protection measures
3. Set up audit logging for all user interactions
4. Create compliance reporting dashboard

### Phase 5: Testing and Launch
1. Comprehensive testing of all new functionality
2. User acceptance testing with existing SafeWork-Kosova users
3. Legal review of compliance measures
4. Gradual rollout with monitoring and feedback collection