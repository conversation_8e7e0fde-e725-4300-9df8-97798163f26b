# === Node.js Dependencies ===
node_modules/
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# === Build Outputs ===
dist/
dist-ssr/
build/
out/
coverage/
tmp/
.cache/
.vite/
.husky/

# === Environment Files & Secrets ===
.env
.env.*
*.local

# === OS-Specific Files ===
.DS_Store
Thumbs.db

# === IDE and Editor Folders ===
.vscode/
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# === TypeScript ===
*.tsbuildinfo

# === Testing & Reports ===
jest.config.js
test-results/
*.lcov
*.log

# === Backup or Archive Folders ===
ver1.1/

# === Asset & Design Cache (optional) ===
*.psd
*.sketch
*.ai
*.xcf

# === Log & Tracking ===
logs/
*.log
*.csv

# === Lock Files (use npm only for Railway compatibility) ===
# Keep package-lock.json for npm
yarn.lock
pnpm-lock.yaml

# === Others (optional tweaks) ===
*.tgz
*.zip
*.bak
*.tmp
*.orig
*.rej
.vercel

.vercel
