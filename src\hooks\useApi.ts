// Custom hook for API calls with loading states
import { useState, useCallback } from 'react';
import type { ApiResponse, LoadingState } from '../types';

interface UseApiState<T> {
  data: T | null;
  loading: LoadingState;
  error: string | null;
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: unknown[]) => Promise<ApiResponse<T>>;
  reset: () => void;
}

/**
 * Custom hook for handling API calls with loading states
 */
export function useApi<T = unknown>(
  apiFunction: (...args: unknown[]) => Promise<ApiResponse<T>>
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: 'idle',
    error: null,
  });

  const execute = useCallback(
    async (...args: unknown[]): Promise<ApiResponse<T>> => {
      setState(prev => ({
        ...prev,
        loading: 'loading',
        error: null,
      }));

      try {
        const response = await apiFunction(...args);

        if (response.success) {
          setState({
            data: response.data || null,
            loading: 'success',
            error: null,
          });
        } else {
          setState({
            data: null,
            loading: 'error',
            error: response.error || 'An error occurred',
          });
        }

        return response;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

        setState({
          data: null,
          loading: 'error',
          error: errorMessage,
        });

        return {
          success: false,
          error: errorMessage,
        };
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: 'idle',
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

/**
 * Hook for handling form submissions with API calls
 */
export function useFormSubmission<T = unknown>(
  apiFunction: (data: unknown) => Promise<ApiResponse<T>>
) {
  const { execute, loading, error, reset } = useApi(apiFunction);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const submit = useCallback(
    async (formData: unknown) => {
      setIsSubmitted(false);
      const response = await execute(formData);

      if (response.success) {
        setIsSubmitted(true);
      }

      return response;
    },
    [execute]
  );

  const resetForm = useCallback(() => {
    reset();
    setIsSubmitted(false);
  }, [reset]);

  return {
    submit,
    loading: loading === 'loading',
    error,
    isSubmitted,
    isSuccess: loading === 'success',
    reset: resetForm,
  };
}

export default useApi;
