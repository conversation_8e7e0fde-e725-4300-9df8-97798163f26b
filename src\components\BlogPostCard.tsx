import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight, Calendar, User } from 'lucide-react';

interface BlogPostCardProps {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  author: string;
  category: string;
}

const BlogPostCard: React.FC<BlogPostCardProps> = ({ id, title, excerpt, image, date, author, category }) => {
  const { t, i18n } = useTranslation('common');
  const currentLang = i18n.language;

  return (
    <article 
      className="rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
      style={{
        backgroundColor: 'rgb(var(--color-surface))',
        borderColor: 'rgb(var(--color-border))'
      }}
    >
      <div className="h-48 overflow-hidden">
        <img 
          src={image} 
          alt={title} 
          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105" 
        />
      </div>
      <div className="p-6">
        <span 
          className="inline-block px-3 py-1 text-xs font-semibold rounded-full mb-3"
          style={{ 
            color: 'rgb(var(--color-secondary-500))',
            backgroundColor: 'rgb(var(--color-secondary-50))'
          }}
        >
          {category}
        </span>
        <h3 
          className="text-xl font-bold mb-2"
          style={{ color: 'rgb(var(--color-text-primary))' }}
        >
          {title}
        </h3>
        <div 
          className="flex items-center text-sm mb-3"
          style={{ color: 'rgb(var(--color-text-muted))' }}
        >
          <div className="flex items-center mr-4">
            <Calendar className="w-4 h-4 mr-1" aria-hidden="true" />
            {date}
          </div>
          <div className="flex items-center">
            <User className="w-4 h-4 mr-1" aria-hidden="true" />
            {author}
          </div>
        </div>
        <p 
          className="mb-4"
          style={{ color: 'rgb(var(--color-text-secondary))' }}
        >
          {excerpt}
        </p>
        <Link 
          to={`/${currentLang}/blog/${id}`} 
          className="inline-flex items-center font-medium transition-colors group focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md px-1 py-1"
          style={{ color: 'rgb(var(--color-secondary-500))' }}
          onMouseEnter={(e) => {
            e.currentTarget.style.color = 'rgb(var(--color-primary-500))';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = 'rgb(var(--color-secondary-500))';
          }}
          aria-label={`Read more about ${title}`}
        >
          {t('button_readMore')}
          <ArrowRight 
            className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" 
            aria-hidden="true"
          />
        </Link>
      </div>
    </article>
  );
};

export default BlogPostCard;