#!/usr/bin/env node

// For development, we'll use a simple approach to run migrations
import Database from 'better-sqlite3';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';

// Database configuration
const DB_DIR = process.env.NODE_ENV === 'production' ? '/tmp' : './data';
const DB_PATH = join(DB_DIR, 'crm.db');

// Ensure database directory exists
if (!existsSync(DB_DIR)) {
  mkdirSync(DB_DIR, { recursive: true });
}

// Simple migration runner
function runMigrations() {
  const db = new Database(DB_PATH);
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');
  
  // Create migrations table
  db.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      version INTEGER PRIMARY KEY,
      name TEXT NOT NULL,
      applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Migration 1: Initial schema
  const migration1Applied = db.prepare('SELECT version FROM migrations WHERE version = 1').get();
  if (!migration1Applied) {
    console.log('Running migration 1: initial_schema');
    
    db.exec(`
      CREATE TABLE IF NOT EXISTS inquiries (
        id TEXT PRIMARY KEY,
        contact_name TEXT NOT NULL,
        contact_email TEXT NOT NULL,
        company_name TEXT,
        inquiry_category TEXT NOT NULL,
        inquiry_subcategory TEXT,
        urgency_level TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT DEFAULT 'new',
        assigned_to TEXT,
        requires_professional INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    db.exec(`
      CREATE TABLE IF NOT EXISTS resources (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        type TEXT NOT NULL,
        file_url TEXT NOT NULL,
        preview_url TEXT,
        file_size INTEGER,
        format TEXT,
        language TEXT DEFAULT 'sq',
        download_count INTEGER DEFAULT 0,
        disclaimer TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    db.exec(`
      CREATE TABLE IF NOT EXISTS user_activities (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        user_email TEXT,
        ip_address TEXT,
        activity_type TEXT NOT NULL,
        resource_id TEXT,
        inquiry_id TEXT,
        metadata TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resource_id) REFERENCES resources(id),
        FOREIGN KEY (inquiry_id) REFERENCES inquiries(id)
      )
    `);
    
    db.prepare('INSERT INTO migrations (version, name) VALUES (1, "initial_schema")').run();
    console.log('Migration 1 completed');
  }

  // Migration 2: Add indexes
  const migration2Applied = db.prepare('SELECT version FROM migrations WHERE version = 2').get();
  if (!migration2Applied) {
    console.log('Running migration 2: add_indexes');
    
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_inquiries_status ON inquiries(status);
      CREATE INDEX IF NOT EXISTS idx_inquiries_category ON inquiries(inquiry_category);
      CREATE INDEX IF NOT EXISTS idx_inquiries_created_at ON inquiries(created_at);
      CREATE INDEX IF NOT EXISTS idx_resources_category ON resources(category);
      CREATE INDEX IF NOT EXISTS idx_resources_type ON resources(type);
      CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
      CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);
    `);
    
    db.prepare('INSERT INTO migrations (version, name) VALUES (2, "add_indexes")').run();
    console.log('Migration 2 completed');
  }

  db.close();
}

function getMigrationStatus() {
  const db = new Database(DB_PATH);
  
  // Create migrations table if it doesn't exist
  db.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      version INTEGER PRIMARY KEY,
      name TEXT NOT NULL,
      applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  const applied = db.prepare('SELECT version, name FROM migrations ORDER BY version').all();
  db.close();
  
  const allMigrations = [
    { version: 1, name: 'initial_schema' },
    { version: 2, name: 'add_indexes' }
  ];
  
  return allMigrations.map(migration => ({
    ...migration,
    applied: applied.some(a => a.version === migration.version)
  }));
}

const command = process.argv[2];
const arg = process.argv[3];

switch (command) {
  case 'up':
    console.log('Running migrations...');
    try {
      runMigrations();
      console.log('Migrations completed successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }
    break;

  case 'down':
    if (!arg) {
      console.error('Please specify target version: npm run migrate down <version>');
      process.exit(1);
    }
    console.log(`Rolling back to version ${arg}...`);
    try {
      rollbackMigrations(parseInt(arg));
      console.log('Rollback completed successfully');
    } catch (error) {
      console.error('Rollback failed:', error);
      process.exit(1);
    }
    break;

  case 'status':
    console.log('Migration status:');
    const status = getMigrationStatus();
    status.forEach(migration => {
      const applied = migration.applied ? '✓' : '✗';
      console.log(`  ${applied} ${migration.version}: ${migration.name}`);
    });
    break;

  default:
    console.log('Usage:');
    console.log('  npm run migrate up      - Run pending migrations');
    console.log('  npm run migrate down <version> - Rollback to version');
    console.log('  npm run migrate status  - Show migration status');
    break;
}