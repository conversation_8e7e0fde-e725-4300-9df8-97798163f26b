import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Quote } from 'lucide-react';
// Import the useTranslation hook
import { useTranslation } from 'react-i18next';

interface Testimonial {
  id: number;
  quote: string;
  author: string;
  position: string;
  company: string;
  image: string;
}

const TestimonialCarousel: React.FC = () => {
  // Initialize the translation hook and specify the 'home' namespace
  const { t } = useTranslation('home');

  // We now build the testimonials array dynamically from our translation file.
  // Memoize to prevent infinite re-renders
  const testimonials: Testimonial[] = useMemo(() => [
    {
      id: 1,
      quote: t('testimonial1_quote'),
      author: t('testimonial1_author'),
      position: t('testimonial1_position'),
      company: t('testimonial1_company'),
      image: "/images/testimonial_1.png"
    },
    {
      id: 2,
      quote: t('testimonial2_quote'),
      author: t('testimonial2_author'),
      position: t('testimonial2_position'),
      company: t('testimonial2_company'),
      image: "/images/testimonial_2.png"
    },
    {
      id: 3,
      quote: t('testimonial3_quote'),
      author: t('testimonial3_author'),
      position: t('testimonial3_position'),
      company: t('testimonial3_company'),
      image: "/images/testimonial_3.png"
    },
    {
      id: 4,
      quote: t('testimonial4_quote'),
      author: t('testimonial4_author'),
      position: t('testimonial4_position'),
      company: t('testimonial4_company'),
      image: "/images/testimonial_4.png"
    },
    {
      id: 5,
      quote: t('testimonial5_quote'),
      author: t('testimonial5_author'),
      position: t('testimonial5_position'),
      company: t('testimonial5_company'),
      image: "/images/testimonial_5.png"
    }
  ], [t]);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const nextSlide = useCallback(() => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prevIndex) => (prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1));
    }
  }, [isTransitioning, testimonials.length]);

  const prevSlide = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prevIndex) => (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1));
    }
  };

  const goToSlide = (index: number) => {
    if (!isTransitioning && index !== currentIndex) {
      setIsTransitioning(true);
      setCurrentIndex(index);
    }
  };

  useEffect(() => {
    const timeout = setTimeout(() => setIsTransitioning(false), 500);
    return () => clearTimeout(timeout);
  }, [currentIndex]);

  useEffect(() => {
    const interval = setInterval(() => nextSlide(), 8000);
    return () => clearInterval(interval);
  }, [currentIndex, isTransitioning, nextSlide]);

  return (
    <div className="relative">
      <div className="mx-auto max-w-4xl px-4 md:px-8">
        <div className="overflow-hidden bg-white dark:bg-gray-700 rounded-xl shadow-lg dark:text-gray-100 border border-gray-100 dark:border-gray-600">
          <div 
            className="transition-transform duration-500 ease-in-out flex"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="w-full flex-shrink-0">
                <div className="flex flex-col md:flex-row p-6 md:p-10">
                  <div className="md:w-1/3 flex justify-center mb-6 md:mb-0">
                    <div className="relative">
                      <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-blue-100">
                        <img 
                          src={testimonial.image} 
                          alt={testimonial.author} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="absolute -top-2 -left-2 bg-orange-500 rounded-full p-2">
                        <Quote className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  </div>
                  <div className="md:w-2/3 md:pl-8">
                    <p className="text-gray-600 dark:text-gray-100 text-lg mb-6 italic">"{testimonial.quote}"</p>
                    <div>
                      <h4 className="font-bold text-gray-900 dark:text-gray-100">{testimonial.author}</h4>
                      <p className="text-gray-500 dark:text-gray-100">{testimonial.position}, {testimonial.company}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <button 
        onClick={prevSlide}
        className="absolute top-1/2 left-0 -translate-y-1/2 bg-white rounded-full p-2 shadow-md text-gray-700 hover:text-blue-800 transition-colors focus:outline-none md:-left-5"
        aria-label="Previous testimonial"
      >
        <ChevronLeft className="w-5 h-5" />
      </button>
      <button 
        onClick={nextSlide}
        className="absolute top-1/2 right-0 -translate-y-1/2 bg-white rounded-full p-2 shadow-md text-gray-700 hover:text-blue-800 transition-colors focus:outline-none md:-right-5"
        aria-label="Next testimonial"
      >
        <ChevronRight className="w-5 h-5" />
      </button>

      {/* Dots Navigation */}
      <div className="flex justify-center mt-6 space-x-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-colors ${
              index === currentIndex ? 'bg-blue-800' : 'bg-gray-300 hover:bg-gray-400'
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default TestimonialCarousel;