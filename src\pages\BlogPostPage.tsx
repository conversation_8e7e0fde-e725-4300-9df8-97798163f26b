import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Calendar, User, Tag, Facebook, Twitter, Linkedin, Clock } from 'lucide-react';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';
import { blogPostsData } from '../data/blogPosts';
import { sanitizeBlogContent, suggestEmailCorrection } from '../utils/securityUtils';
import { validateNewsletterEmail } from '../lib/validators';
import { enhanceBlogContent } from '../utils/blogContentEnhancer';

const MILAZIM_PROFILE_IMAGE = "/images/Milazim.jpg";

const BlogPostPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const { t, i18n } = useTranslation('blog');
  const navigate = useNavigate();
  const currentLang = i18n.language;

  // Newsletter subscription state
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [message, setMessage] = useState('');

  // Reading progress state
  const [readingProgress, setReadingProgress] = useState(0);



  const postMaster = blogPostsData.find(post => post.id === postId);

  if (!postMaster) {
    return (
      <div className="min-h-[70vh] flex items-center justify-center px-4 py-12 bg-white dark:bg-gray-900">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('detail_page_not_found')}</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-8">{t('detail_page_not_found_desc')}</p>
          <Link to={`/${currentLang}/blog`} className="btn-primary">{t('back_to_blog')}</Link>
        </div>
      </div>
    );
  }

  const post = {
    ...postMaster,
    author: t(postMaster.author), // Translate author name
    authorTitle: t(postMaster.authorTitleKey), // Translate author title
    readTime: `${postMaster.readTimeMinutes} ${t('read_time_suffix')}`, // Construct read time
    title: t(`${postMaster.id}.title`),
    excerpt: t(`${postMaster.id}.excerpt`),
    content: t(`${postMaster.id}.content`),
    category: t(`${postMaster.id}.category`),
    authorBio: t(`${postMaster.id}.authorBio`),
  };

  const relatedPosts = blogPostsData
    .filter(p => p.id !== post.id && p.tags?.some(tag => post.tags?.includes(tag)))
    .slice(0, 3);

  const handleTagClick = (tag: string) => {
    navigate(`/${currentLang}/blog?tag=${encodeURIComponent(tag)}`);
  };

  // Reading progress tracking
  useEffect(() => {
    const updateReadingProgress = () => {
      const article = document.querySelector('.blog-content-enhanced');
      if (!article) return;

      const articleTop = article.offsetTop;
      const articleHeight = article.offsetHeight;
      const windowHeight = window.innerHeight;
      const scrollTop = window.scrollY;

      const articleBottom = articleTop + articleHeight;
      const windowBottom = scrollTop + windowHeight;

      if (scrollTop < articleTop) {
        setReadingProgress(0);
      } else if (windowBottom > articleBottom) {
        setReadingProgress(100);
      } else {
        const progress = ((scrollTop - articleTop) / (articleHeight - windowHeight)) * 100;
        setReadingProgress(Math.min(Math.max(progress, 0), 100));
      }
    };

    window.addEventListener('scroll', updateReadingProgress);
    window.addEventListener('resize', updateReadingProgress);
    updateReadingProgress(); // Initial calculation

    return () => {
      window.removeEventListener('scroll', updateReadingProgress);
      window.removeEventListener('resize', updateReadingProgress);
    };
  }, []);



  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage('');

    // Client-side validation using new validation system
    const validation = validateNewsletterEmail(email);
    if (!validation.isValid) {
      const suggestion = suggestEmailCorrection(email.trim());
      if (suggestion) {
        setMessage(t('sidebar_subscribe_error_typo', { suggestion }));
      } else {
        setMessage(validation.error || t('sidebar_subscribe_error_invalid'));
      }
      return;
    }

    setIsSubscribing(true);

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      // Log the raw response for debugging
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      // Get response text first to see what we're actually getting
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        console.error('Response was:', responseText);
        throw new Error('Server returned invalid response');
      }

      if (response.ok) {
        setMessage(t('sidebar_subscribe_success'));
        setEmail('');
      } else {
        console.error('Subscription failed:', response.status, data);
        setMessage(data.error || data.message || t('sidebar_subscribe_error_invalid'));
      }
    } catch (error) {
      console.error('Network error during subscription:', error);
      setMessage(t('sidebar_subscribe_error_general'));
    } finally {
      setIsSubscribing(false);
    }
  };

  return (
    <div>
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div
          className="h-full bg-gradient-to-r from-orange-500 to-blue-600 transition-all duration-300 ease-out"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      <Hero title={post.title} subtitle={post.excerpt} ctaText={t('page_cta')} ctaLink="#subscribe" bgImage={post.image} />
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 md:px-6 py-4">
          <Link to={`/${currentLang}/blog`} className="inline-flex items-center text-blue-800 dark:text-blue-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('back_to_blog')}
          </Link>
        </div>
      </div>
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Main Content */}
            <div className="lg:w-2/3">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 md:p-12">
                {/* Article Header */}
                <div className="mb-12 text-center">
                  <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-6"
                       style={{
                         backgroundColor: 'rgb(var(--color-secondary-50))',
                         color: 'rgb(var(--color-secondary-700))'
                       }}>
                    <Tag className="w-4 h-4 mr-2" />
                    {post.category}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight"
                      style={{ color: 'rgb(var(--color-text-primary))' }}>
                    {post.title}
                  </h1>
                  <div className="flex flex-wrap items-center justify-center gap-8 text-sm mb-8"
                       style={{ color: 'rgb(var(--color-text-muted))' }}>
                    <div className="flex items-center">
                      <User className="w-5 h-5 mr-2" />
                      <span className="font-medium">{post.author}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-5 h-5 mr-2" />
                      <span>{post.date}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 mr-2" />
                      <span className="font-medium">{post.readTime}</span>
                    </div>
                  </div>
                  <div className="w-24 h-1 mx-auto rounded-full"
                       style={{ background: 'linear-gradient(90deg, rgb(var(--color-primary-500)), rgb(var(--color-secondary-500)))' }}>
                  </div>
                </div>

                {/* Article Content */}
                <div className="space-y-8">
                  <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
                    <p className="text-gray-700 leading-relaxed font-medium">{post.excerpt}</p>
                  </div>

                  <div
                    className="blog-content-enhanced max-w-none"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeBlogContent(
                        enhanceBlogContent(post.content || '')
                      )
                    }}
                  />
                </div>

                {/* Share Section */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                      </svg>
                    </span>
                    {t('share_article')}
                  </h3>
                  <div className="flex space-x-4 pl-11">
                    <button
                      onClick={() => {
                        const url = `https://www.facebook.com/sharer/sharer.php?u=${window.location.href}`;
                        try {
                          window.open(url, '_blank', 'noopener,noreferrer');
                        } catch {
                          navigator.clipboard?.writeText(window.location.href).then(() => {
                            alert('Link copied to clipboard for sharing');
                          });
                        }
                      }}
                      className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
                      aria-label={t('share_facebook')}
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => {
                        const url = `https://twitter.com/intent/tweet?url=${window.location.href}&text=${encodeURIComponent(post.title)}`;
                        try {
                          window.open(url, '_blank', 'noopener,noreferrer');
                        } catch {
                          navigator.clipboard?.writeText(`${post.title} - ${window.location.href}`).then(() => {
                            alert('Content copied to clipboard for sharing');
                          });
                        }
                      }}
                      className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
                      aria-label={t('share_twitter')}
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => {
                        const url = `https://www.linkedin.com/shareArticle?mini=true&url=${window.location.href}&title=${encodeURIComponent(post.title)}`;
                        try {
                          window.open(url, '_blank', 'noopener,noreferrer');
                        } catch {
                          navigator.clipboard?.writeText(`${post.title} - ${window.location.href}`).then(() => {
                            alert('Content copied to clipboard for sharing');
                          });
                        }
                      }}
                      className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
                      aria-label={t('share_linkedin')}
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Author Bio */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">
                      <User className="w-4 h-4" />
                    </span>
                    {t('written_by')}
                  </h3>
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 ml-11">
                    <div className="flex items-center mb-4">
                      <img src={MILAZIM_PROFILE_IMAGE} alt={post.author} className="w-16 h-16 rounded-full object-cover mr-4 shadow-md" />
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">{post.author}</h4>
                        <p className="text-blue-600 text-sm font-medium">{post.authorTitle}</p>
                      </div>
                    </div>
                    <p className="text-gray-700 leading-relaxed">{post.authorBio}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3 space-y-8">
              {post.tags && post.tags.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">{t('tags_title')}</h3>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <button
                        key={index}
                        onClick={() => handleTagClick(tag)}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-blue-100 hover:text-blue-800 transition-colors"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {relatedPosts.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-6">{t('related_posts_title')}</h3>
                  <div className="space-y-6">
                    {relatedPosts.map((relatedPost) => (
                      <Link
                        key={relatedPost.id}
                        to={`/${currentLang}/blog/${relatedPost.id}`}
                        className="block group transition-opacity hover:opacity-80"
                      >
                        <div className="flex items-start space-x-4">
                          <img
                            src={relatedPost.image}
                            alt={t(`${relatedPost.id}.title`)}
                            className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                          />
                          <div className='flex-1'>
                            <h4 className="font-semibold text-gray-900 mb-1 text-base leading-tight group-hover:text-blue-800 transition-colors">
                              {t(`${relatedPost.id}.title`)}
                            </h4>
                            <p className="text-xs text-gray-500">{relatedPost.date}</p>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              <div id="subscribe" className="sticky top-24 bg-blue-800 rounded-xl p-6 text-white shadow-lg">
                <h3 className="text-lg font-bold mb-4">{t('sidebar_subscribe_title')}</h3>
                <p className="text-blue-100 mb-6">{t('sidebar_subscribe_desc')}</p>
                <form onSubmit={handleSubscribe} className="space-y-4">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('sidebar_subscribe_placeholder')}
                    className="w-full px-4 py-2 rounded-md text-gray-800 bg-white focus:ring-2 focus:ring-orange-500 focus:outline-none"
                    required
                  />
                  <button
                    type="submit"
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                    disabled={isSubscribing}
                  >
                    {isSubscribing ? 'Subscribing...' : t('sidebar_subscribe_button')}
                  </button>
                  {message && <p className="text-sm text-center">{message}</p>}
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
      <CtaSection title={t('cta_title')} description={t('cta_subtitle')} ctaText={t('cta_button')} ctaLink="/contact" />
    </div>
  );
};

export default BlogPostPage;