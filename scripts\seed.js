#!/usr/bin/env node

import Database from 'better-sqlite3';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { randomUUID } from 'crypto';

// Database configuration
const DB_DIR = process.env.NODE_ENV === 'production' ? '/tmp' : './data';
const DB_PATH = join(DB_DIR, 'crm.db');

// Ensure database directory exists
if (!existsSync(DB_DIR)) {
    mkdirSync(DB_DIR, { recursive: true });
}

function seedDatabase() {
    const db = new Database(DB_PATH);

    console.log('Seeding database with sample data...');

    // Clear existing data
    db.exec('DELETE FROM user_activities');
    db.exec('DELETE FROM inquiries');
    db.exec('DELETE FROM resources');

    // Seed resources
    const resourceStmt = db.prepare(`
    INSERT INTO resources (
      id, title, description, category, type, file_url, 
      preview_url, file_size, format, language, download_count, disclaimer
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

    const sampleResources = [
        {
            id: randomUUID(),
            title: 'Workplace Risk Assessment Template',
            description: 'Comprehensive template for conducting workplace risk assessments',
            category: 'risk-assessment',
            type: 'template',
            file_url: '/resources/risk-assessment-template.pdf',
            preview_url: '/resources/previews/risk-assessment.jpg',
            file_size: 512000,
            format: 'pdf',
            language: 'sq',
            download_count: 15,
            disclaimer: 'For educational purposes only. Consult qualified professionals for specific workplace assessments.'
        },
        {
            id: randomUUID(),
            title: 'Safety Training Checklist',
            description: 'Essential checklist for workplace safety training programs',
            category: 'training-materials',
            type: 'checklist',
            file_url: '/resources/safety-training-checklist.pdf',
            preview_url: '/resources/previews/training-checklist.jpg',
            file_size: 256000,
            format: 'pdf',
            language: 'sq',
            download_count: 23,
            disclaimer: 'Educational resource. Professional training consultation recommended.'
        },
        {
            id: randomUUID(),
            title: 'Emergency Evacuation Procedures',
            description: 'Step-by-step guide for emergency evacuation planning',
            category: 'emergency-procedures',
            type: 'guide',
            file_url: '/resources/emergency-evacuation-guide.pdf',
            preview_url: '/resources/previews/evacuation-guide.jpg',
            file_size: 1024000,
            format: 'pdf',
            language: 'sq',
            download_count: 8,
            disclaimer: 'General guidance only. Consult emergency planning professionals for site-specific plans.'
        },
        {
            id: randomUUID(),
            title: 'Job Safety Analysis Template',
            description: 'Template for conducting Job Safety Analysis (JSA)',
            category: 'jsa-templates',
            type: 'template',
            file_url: '/resources/jsa-template.pdf',
            preview_url: '/resources/previews/jsa-template.jpg',
            file_size: 384000,
            format: 'pdf',
            language: 'sq',
            download_count: 31,
            disclaimer: 'Educational template. Professional safety consultation recommended for implementation.'
        }
    ];

    sampleResources.forEach(resource => {
        resourceStmt.run(
            resource.id, resource.title, resource.description, resource.category,
            resource.type, resource.file_url, resource.preview_url, resource.file_size,
            resource.format, resource.language, resource.download_count, resource.disclaimer
        );
    });

    console.log(`Seeded ${sampleResources.length} resources`);

    // Seed inquiries
    const inquiryStmt = db.prepare(`
    INSERT INTO inquiries (
      id, contact_name, contact_email, company_name, inquiry_category,
      inquiry_subcategory, urgency_level, description, status, requires_professional
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

    const sampleInquiries = [
        {
            id: randomUUID(),
            contact_name: 'Agron Berisha',
            contact_email: '<EMAIL>',
            company_name: 'Berisha Construction',
            inquiry_category: 'risk-assessment',
            inquiry_subcategory: 'construction-safety',
            urgency_level: 'high',
            description: 'Need guidance on construction site safety risk assessment procedures',
            status: 'new',
            requires_professional: 1
        },
        {
            id: randomUUID(),
            contact_name: 'Fatmire Krasniqi',
            contact_email: '<EMAIL>',
            company_name: 'Kosovo Manufacturing Ltd',
            inquiry_category: 'training',
            inquiry_subcategory: 'worker-safety',
            urgency_level: 'medium',
            description: 'Looking for workplace safety training materials for manufacturing workers',
            status: 'in-progress',
            requires_professional: 0
        },
        {
            id: randomUUID(),
            contact_name: 'Driton Ahmeti',
            contact_email: '<EMAIL>',
            company_name: 'Ahmeti Logistics',
            inquiry_category: 'compliance',
            inquiry_subcategory: 'regulatory-requirements',
            urgency_level: 'low',
            description: 'Information needed about Kosovo workplace safety compliance requirements',
            status: 'responded',
            requires_professional: 1
        }
    ];

    sampleInquiries.forEach(inquiry => {
        inquiryStmt.run(
            inquiry.id, inquiry.contact_name, inquiry.contact_email, inquiry.company_name,
            inquiry.inquiry_category, inquiry.inquiry_subcategory, inquiry.urgency_level,
            inquiry.description, inquiry.status, inquiry.requires_professional
        );
    });

    console.log(`Seeded ${sampleInquiries.length} inquiries`);

    // Seed user activities
    const activityStmt = db.prepare(`
    INSERT INTO user_activities (
      id, session_id, user_email, ip_address, activity_type, resource_id, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?)
  `);

    const sampleActivities = [
        {
            id: randomUUID(),
            session_id: 'session-001',
            user_email: '<EMAIL>',
            ip_address: '*************',
            activity_type: 'resource_download',
            resource_id: sampleResources[0].id,
            metadata: JSON.stringify({ browser: 'Chrome', referrer: '/resources' })
        },
        {
            id: randomUUID(),
            session_id: 'session-002',
            user_email: '<EMAIL>',
            ip_address: '*************',
            activity_type: 'page_view',
            resource_id: null,
            metadata: JSON.stringify({ page: '/about', duration: 45 })
        },
        {
            id: randomUUID(),
            session_id: 'session-003',
            user_email: null,
            ip_address: '*************',
            activity_type: 'search',
            resource_id: null,
            metadata: JSON.stringify({ query: 'safety training', results: 5 })
        }
    ];

    sampleActivities.forEach(activity => {
        activityStmt.run(
            activity.id, activity.session_id, activity.user_email, activity.ip_address,
            activity.activity_type, activity.resource_id, activity.metadata
        );
    });

    console.log(`Seeded ${sampleActivities.length} user activities`);

    db.close();
    console.log('Database seeding completed successfully!');
}

seedDatabase();