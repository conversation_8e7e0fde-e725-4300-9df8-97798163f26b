import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight, X } from 'lucide-react';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';
import { ALL_SERVICES, getServiceIcon, getServiceLink } from '../constants/services';
import { INDUSTRIES_CONFIG } from '../constants/industries';
import { useSEO, generateStructuredData } from '../contexts/SEOContext';

const ServicesPage: React.FC = () => {
  const { t, i18n } = useTranslation(['services', 'common']);
  const currentLang = i18n.language;
  const { updateSEO } = useSEO();
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  // Update SEO for services page
  useEffect(() => {
    const serviceSchema = generateStructuredData('Service', {
      name: currentLang === 'sq' ? 'Shërbimet e Sigurisë në Punë' : 'Workplace Safety Services',
      provider: {
        '@type': 'Organization',
        name: 'SafeWork Kosova'
      },
      description: currentLang === 'sq'
        ? 'Shërbime gjithëpërfshirëse të sigurisë në punë duke përfshirë vlerësimin e rrezikut, trajnimet, auditimin dhe konsulencën për bizneset në Kosovë.'
        : 'Comprehensive workplace safety services including risk assessments, training, auditing and consulting for businesses in Kosovo.',
      serviceType: currentLang === 'sq' ? 'Siguria në Punë' : 'Workplace Safety',
      areaServed: {
        '@type': 'Country',
        name: 'Kosovo'
      }
    });

    updateSEO({
      title: currentLang === 'sq'
        ? 'Shërbimet e Sigurisë në Punë - SafeWork Kosova'
        : 'Workplace Safety Services - SafeWork Kosova',
      description: currentLang === 'sq'
        ? 'Shërbime profesionale të sigurisë në punë: vlerësim rreziku, trajnime, auditim, konsulencë dhe zgjidhje të personalizuara për bizneset në Kosovë.'
        : 'Professional workplace safety services: risk assessments, training, auditing, consulting and customized solutions for businesses in Kosovo.',
      keywords: currentLang === 'sq'
        ? ['shërbime siguria', 'vlerësim rreziku', 'trajnime siguria', 'auditim siguria', 'konsulencë', 'JSA', 'teknologji siguria']
        : ['safety services', 'risk assessment', 'safety training', 'safety auditing', 'consulting', 'JSA', 'safety technology'],
      type: 'service',
      structuredData: serviceSchema,
      openGraph: {
        title: currentLang === 'sq'
          ? 'Shërbimet e Sigurisë në Punë - SafeWork Kosova'
          : 'Workplace Safety Services - SafeWork Kosova',
        description: currentLang === 'sq'
          ? 'Shërbime profesionale të sigurisë duke përfshirë teknologjinë e avancuar JSA Generator.'
          : 'Professional safety services including advanced JSA Generator technology.',
        type: 'website'
      }
    });
  }, [currentLang, updateSEO]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVideoModalOpen) {
        setIsVideoModalOpen(false);
      }
    };

    if (isVideoModalOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isVideoModalOpen]);

  // Use shared services configuration
  const services = ALL_SERVICES.map(service => ({
    id: service.id,
    icon: getServiceIcon(service.id, 'w-10 h-10'),
    link: getServiceLink(service.id, currentLang)
  }));

  // Use shared industries configuration
  const industries = INDUSTRIES_CONFIG.map(industry => industry.id);
  const approachSteps = ['step1', 'step2', 'step3', 'step4', 'step5'];
  const faqs = ['faq1', 'faq2', 'faq3', 'faq4', 'faq5'];

  // VideoModal Component
  const VideoModal = () => {
    if (!isVideoModalOpen) return null;

    return (
      <div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
        onClick={() => setIsVideoModalOpen(false)}
      >
        <div className="relative w-full max-w-6xl mx-4 aspect-video">
          {/* Close button */}
          <button
            onClick={() => setIsVideoModalOpen(false)}
            className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
            aria-label="Close video"
          >
            <X className="w-8 h-8" />
          </button>

          {/* Video container */}
          <div
            className="w-full h-full bg-black rounded-lg overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <video
              controls
              autoPlay
              className="w-full h-full object-contain"
              preload="metadata"
            >
              <source src="/videos/jsa-demo.mp4" type="video/mp4" />
              <source src="/videos/jsa-demo.mkv" type="video/x-matroska" />
              {/* Fallback message for unsupported browsers */}
              <div className="flex items-center justify-center h-full bg-gray-900">
                <div className="text-center p-4">
                  <div className="w-16 h-16 bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300 font-medium">{t('jsa_generator_video_placeholder')}</p>
                  <p className="text-sm text-gray-400 mt-2">Your browser doesn't support this video format</p>
                  <a
                    href="/videos/jsa-demo.mkv"
                    download
                    className="inline-block mt-3 px-4 py-2 bg-blue-800 text-white rounded-lg text-sm hover:bg-blue-900 transition-colors"
                  >
                    Download Video
                  </a>
                </div>
              </div>
            </video>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="dark:bg-gray-800">
      <Hero
        title={t('page_title')}
        subtitle={t('page_subtitle')}
        ctaText={t('cta_button')}
        ctaLink={`/${currentLang}/contact#formular-kontakti`}
        bgImage="/images/services-hero.jpg"
      />

      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center gap-12 mb-16">
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('intro_title')}</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-4">{t('intro_p1')}</p>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-4">{t('intro_p2')}</p>
              <p className="text-lg text-gray-600 dark:text-gray-300">{t('intro_p3')}</p>
            </div>
            <div className="md:w-1/2">
              <img
                src="/images/services-intro.jpg"
                alt={t('intro_title')}
                className="rounded-lg shadow-lg w-full"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div key={service.id} className="bg-white dark:bg-gray-700 rounded-lg shadow-md border border-gray-100 dark:border-gray-600 overflow-hidden">
                <div className="p-6">
                  <div className="mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">{t(`${service.id}.title`)}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{t(`${service.id}.description`)}</p>
                  <ul className="space-y-2 mb-6">
                    {(t(`${service.id}.features`, { returnObjects: true }) as string[] || []).map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-orange-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link
                    to={`/${currentLang}/services/${service.id}`}
                    className="inline-flex items-center text-blue-800 dark:text-blue-300 font-medium hover:text-orange-500 transition-colors"
                  >
                    {t('common:button_learnMore')}
                    <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* JSA Generator Section */}
      <section className="py-16 bg-blue-50 dark:bg-gray-700">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('jsa_generator_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('jsa_generator_subtitle')}</p>
          </div>

          <div className="grid md:grid-cols-5 gap-12 items-center">
            <div className="md:col-span-2">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('jsa_generator_app_title')}</h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">{t('jsa_generator_description')}</p>

              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-3 mt-1">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{t('jsa_generator_feature1_title')}</h4>
                    <p className="text-gray-600 dark:text-gray-300">{t('jsa_generator_feature1_desc')}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-3 mt-1">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{t('jsa_generator_feature2_title')}</h4>
                    <p className="text-gray-600 dark:text-gray-300">{t('jsa_generator_feature2_desc')}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-3 mt-1">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{t('jsa_generator_feature3_title')}</h4>
                    <p className="text-gray-600 dark:text-gray-300">{t('jsa_generator_feature3_desc')}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-3 mt-1">
                    <span className="text-white text-sm font-bold">4</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{t('jsa_generator_feature4_title')}</h4>
                    <p className="text-gray-600 dark:text-gray-300">{t('jsa_generator_feature4_desc')}</p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={() => setIsVideoModalOpen(true)}
                  className="bg-blue-800 hover:bg-blue-900 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {t('jsa_generator_cta_demo')}
                </button>
                <Link
                  to={`/${currentLang}/contact`}
                  className="border border-blue-800 text-blue-800 hover:bg-blue-800 hover:text-white dark:border-orange-400 dark:text-orange-400 dark:hover:bg-orange-400 dark:hover:text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors text-center"
                >
                  {t('jsa_generator_cta_contact')}
                </Link>
              </div>
            </div>

            <div className="relative md:col-span-3">
              <div className="aspect-video bg-gray-200 dark:bg-gray-600 rounded-lg shadow-lg overflow-hidden relative group">
                {/* Play button overlay */}
                <div
                  className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer z-10"
                  onClick={() => setIsVideoModalOpen(true)}
                >
                  <div className="w-20 h-20 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>

                {/* JSA Generator Demo Video */}
                <video
                  id="jsa-demo-video"
                  controls
                  className="w-full h-full object-cover rounded-lg cursor-pointer"
                  preload="metadata"
                  style={{ backgroundColor: '#f3f4f6' }}
                  onClick={(e) => {
                    e.preventDefault();
                    setIsVideoModalOpen(true);
                  }}
                  onPlay={(e) => {
                    e.preventDefault();
                    e.currentTarget.pause();
                    setIsVideoModalOpen(true);
                  }}
                >
                  <source src="/videos/jsa-demo.mp4" type="video/mp4" />
                  <source src="/videos/jsa-demo.mkv" type="video/x-matroska" />
                  {/* Fallback message for unsupported browsers */}
                  <div className="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-600">
                    <div className="text-center p-4">
                      <div className="w-16 h-16 bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-gray-500 dark:text-gray-400 font-medium">{t('jsa_generator_video_placeholder')}</p>
                      <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">Your browser doesn't support this video format</p>
                      <a
                        href="/videos/jsa-demo.mkv"
                        download
                        className="inline-block mt-3 px-4 py-2 bg-blue-800 text-white rounded-lg text-sm hover:bg-blue-900 transition-colors"
                      >
                        Download Video
                      </a>
                    </div>
                  </div>
                </video>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('industries_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('industries_subtitle')}</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {industries.map(industry => (
              <div key={industry} className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-800 dark:text-gray-300 font-bold">{t(`industry_${industry}`).substring(0, 3).toUpperCase()}</span>
                </div>
                <h3 className="font-bold text-gray-900 dark:text-gray-100 mb-2">{t(`industry_${industry}`)}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('approach_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('approach_subtitle')}</p>
          </div>
          <div className="flex flex-col space-y-8 max-w-4xl mx-auto">
            {approachSteps.map((step, index) => (
              <div key={step} className="flex flex-col md:flex-row items-center bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                <div className="md:w-1/3 bg-blue-800 dark:bg-gray-900 text-white dark:text-white p-6 flex flex-col items-center justify-center h-full">
                  <div className="rounded-full bg-blue-700 dark:bg-gray-950 w-12 h-12 flex items-center justify-center mb-3">
                    <span className="font-bold text-xl text-white dark:text-gray-100">{index + 1}</span>
                  </div>
                  <h3 className="text-xl font-bold text-center">{t(`${step}_title`)}</h3>
                </div>
                <div className="md:w-2/3 p-6">
                  <p className="text-gray-600 dark:text-gray-300">{t(`${step}_desc`)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('faq_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('faq_subtitle')}</p>
          </div>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">{t(`${faq}_q`)}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{t(`${faq}_a`)}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-10">
              <a href="mailto:<EMAIL>" className="btn-primary inline-block">{t('faq_cta_button')}</a>
            </div>
          </div>
        </div>
      </section>

      <CtaSection 
        title={t('cta_title')} 
        description={t('cta_subtitle')} 
        ctaText={t('cta_button')} 
        ctaLink={`/${currentLang}/contact#formular-kontakti`} 
      />

      {/* Video Modal */}
      <VideoModal />
    </div>
  );
};

export default ServicesPage;