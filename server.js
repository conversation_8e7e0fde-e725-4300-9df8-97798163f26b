
import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { body, validationResult } from 'express-validator';
import { Resend } from 'resend';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const resend = new Resend(process.env.RESEND_API_KEY);

// Trust proxy for Railway (fixes contact form proxy issues)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration - more restrictive
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? ['https://www.safework-kosova.com', 'https://safework-kosova.com']
    : ['http://localhost:5173', 'http://127.0.0.1:5173'],
  credentials: true,
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));

// Rate limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const contactLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // limit each IP to 5 contact form submissions per hour
  message: 'Too many contact form submissions, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const subscribeLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each IP to 10 newsletter subscriptions per hour (increased for testing)
  message: 'Too many subscription attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(generalLimiter);
app.use(express.json({ limit: '10mb' }));

// Serve static files from the React app build directory
app.use(express.static('dist'));

// Environment variables validation
const requiredEnvVars = ['RESEND_API_KEY'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

// Get email recipients from environment variables
const getEmailRecipients = () => {
  const recipients = process.env.EMAIL_RECIPIENTS || process.env.CONTACT_EMAIL || '<EMAIL>';
  return recipients.split(',').map(email => email.trim());
};

// Common email domains and their common typos
const DOMAIN_TYPOS = {
  'gmai.com': 'gmail.com',
  'gmial.com': 'gmail.com',
  'gmaill.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'yahoo.co': 'yahoo.com',
  'hotmai.com': 'hotmail.com',
  'hotmial.com': 'hotmail.com',
  'hotml.com': 'hotmail.com',
  'hotmil.com': 'hotmail.com',
  'hotmail.co': 'hotmail.com',
  'outlok.com': 'outlook.com',
  'outlook.co': 'outlook.com',
  'outloo.com': 'outlook.com'
};

// Custom email validation function (more strict than express-validator)
const isValidEmailCustom = (email) => {
  if (!email || typeof email !== 'string') return false;

  // More robust email validation regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // Additional checks
  if (email.length > 254) return false; // RFC 5321 limit
  if (email.includes('..')) return false; // No consecutive dots
  if (email.startsWith('.') || email.endsWith('.')) return false; // No leading/trailing dots
  if (email.includes('@.') || email.includes('.@')) return false; // No dots adjacent to @

  // Basic regex validation
  if (!emailRegex.test(email)) return false;

  // Extract domain for additional validation
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;

  // Check for common typos
  if (DOMAIN_TYPOS[domain]) {
    return false; // Reject known typos
  }

  // Require minimum domain length (helps catch obvious typos)
  if (domain.length < 4) return false; // e.g., "a.b" is too short

  // Require at least one dot in domain
  if (!domain.includes('.')) return false;

  // Check TLD length (most TLDs are 2-6 characters)
  const tld = domain.split('.').pop();
  if (!tld || tld.length < 2 || tld.length > 6) return false;

  return true;
};

// Input validation middleware for contact form
const validateContactForm = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\u00C0-\u017F\u0100-\u024F\u1E00-\u1EFF]+$/)
    .withMessage('Name contains invalid characters'),
  body('email')
    .trim()
    .custom((value) => {
      if (!isValidEmailCustom(value)) {
        throw new Error('Please provide a valid email address');
      }
      return true;
    }),
  body('phone')
    .optional()
    .trim()
    .matches(/^[\d\s\-()]+$/)
    .isLength({ max: 20 })
    .withMessage('Phone number contains invalid characters'),
  body('company')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Company name is too long'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Message must be between 10 and 2000 characters'),
  body('service')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Service selection is required'),
];

// Utility function to escape HTML in email content
const escapeHtml = (text) => {
  if (!text) return '';
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

app.post('/api/contact', contactLimiter, validateContactForm, async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array().map(err => err.msg)
    });
  }

  const { name, email, phone, company, message, service } = req.body;

  try {
    const recipients = getEmailRecipients();

    // Escape all user input for safe HTML rendering
    const safeData = {
      name: escapeHtml(name),
      email: escapeHtml(email),
      phone: escapeHtml(phone || 'Not provided'),
      company: escapeHtml(company || 'Not provided'),
      service: escapeHtml(service),
      message: escapeHtml(message).replace(/\n/g, '<br>')
    };

    const { error } = await resend.emails.send({
  from: 'Contact Form <<EMAIL>>',
      to: recipients,
      subject: `New Contact Form Submission - ${safeData.service}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <table style="border-collapse: collapse; width: 100%; max-width: 600px;">
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Name:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.name}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Email:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.email}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Phone:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.phone}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Company:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.company}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Service:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.service}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold; vertical-align: top;">Message:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeData.message}</td></tr>
        </table>
        <p style="margin-top: 20px; font-size: 12px; color: #666;">
          Submitted at: ${new Date().toLocaleString()}<br>
          IP Address: ${req.ip || 'Unknown'}
        </p>
      `,
    });

    if (error) {
      console.error('Resend error:', error);
      return res.status(500).json({ error: 'Failed to send email. Please try again later.' });
    }

    res.status(200).json({ message: 'Email sent successfully!' });
  } catch (err) {
    console.error('Contact form error:', err);
    res.status(500).json({ error: 'An unexpected error occurred. Please try again later.' });
  }
});

// Input validation middleware for newsletter subscription
const validateSubscription = [
  body('email')
    .trim()
    .custom((value) => {
      if (!isValidEmailCustom(value)) {
        throw new Error('Please provide a valid email address');
      }
      return true;
    }),
];

app.post('/api/subscribe', subscribeLimiter, validateSubscription, async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Invalid email address',
      details: errors.array().map(err => err.msg)
    });
  }

  const { email } = req.body;
  console.log('📧 Newsletter subscription request received');

  try {
    // API key validation (removed sensitive logging)
    if (!process.env.RESEND_API_KEY) {
      console.error('❌ Resend API key not configured');
      return res.status(500).json({ message: 'Email service not configured' });
    }

    const recipients = getEmailRecipients();
    const safeEmail = escapeHtml(email);

    // Send notification email to website owner/developer
    console.log('📤 Sending notification email to developer...');
    const developerResult = await resend.emails.send({
  from: 'Newsletter Notifications <<EMAIL>>',
      to: recipients,
      subject: 'New Newsletter Subscription',
      html: `
        <h2>New Newsletter Subscription</h2>
        <p>Someone just subscribed to your SafeWork Kosova newsletter!</p>
        <table style="border-collapse: collapse; margin: 20px 0;">
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Email:</td><td style="padding: 8px; border: 1px solid #ddd;">${safeEmail}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Subscription Date:</td><td style="padding: 8px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Source:</td><td style="padding: 8px; border: 1px solid #ddd;">SafeWork Kosova Website</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">IP Address:</td><td style="padding: 8px; border: 1px solid #ddd;">${req.ip || 'Unknown'}</td></tr>
        </table>
        <hr>
        <p><small>This is an automated notification from your website's newsletter subscription system.</small></p>
        <p><em>Note: Confirmation email to subscriber will be sent once domain is verified.</em></p>
      `,
    });

    if (developerResult.error) {
      console.error('❌ Error sending developer email:', developerResult.error);
      return res.status(500).json({ message: 'Error sending notification email', details: developerResult.error });
    } else {
      console.log('✅ Developer notification email sent successfully:', developerResult.data);
    }

    // Try to send confirmation email to subscriber (will only work if it's your email or domain is verified)
    console.log('📤 Attempting to send confirmation email to subscriber...');
    try {
      const subscriberResult = await resend.emails.send({
  from: 'Safework Kosova <<EMAIL>>',
        to: [email],
        subject: 'Subscription Confirmation',
        html: `
          <h2>Welcome to the Safework Kosova Newsletter!</h2>
          <p>Thank you for subscribing. You will now receive the latest news and updates from us.</p>
        `,
      });

      if (subscriberResult.error) {
        console.log('⚠️ Could not send confirmation email to subscriber (domain verification needed):', subscriberResult.error.message);
        // Don't return error - the subscription was still recorded
      } else {
        console.log('✅ Subscriber email sent successfully:', subscriberResult.data);
      }
    } catch (subscriberError) {
      console.log('⚠️ Could not send confirmation email to subscriber:', subscriberError.message);
      // Don't return error - the subscription was still recorded
    }

    console.log('✅ Newsletter subscription completed successfully');
    res.status(200).json({ message: 'Subscription successful!' });

  } catch (error) {
    console.error('💥 Unexpected error in subscription process:', error);
    res.status(500).json({ message: 'Error processing subscription', details: error.message });
  }
});

// Test endpoint to verify Resend setup (protected)
app.post('/api/test-email', contactLimiter, async (req, res) => {
  console.log('🧪 Testing email functionality...');

  // Simple authentication check for test endpoint
  const testKey = req.headers['x-test-key'];
  if (testKey !== process.env.TEST_EMAIL_KEY) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const recipients = getEmailRecipients();

    const result = await resend.emails.send({
  from: 'Test <<EMAIL>>',
      to: recipients,
      subject: 'Test Email - SafeWork Kosova',
      html: `
        <h2>Email Test Successful!</h2>
        <p>This is a test email to verify your Resend configuration.</p>
        <table style="border-collapse: collapse; margin: 20px 0;">
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Test Time:</td><td style="padding: 8px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Server:</td><td style="padding: 8px; border: 1px solid #ddd;">SafeWork Kosova API</td></tr>
          <tr><td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Environment:</td><td style="padding: 8px; border: 1px solid #ddd;">${process.env.NODE_ENV || 'development'}</td></tr>
        </table>
        <p>If you receive this, your email system is working correctly.</p>
      `,
    });

    if (result.error) {
      console.error('❌ Test email failed:', result.error);
      return res.status(500).json({ error: 'Test email failed' });
    }

    console.log('✅ Test email sent successfully:', result.data);
    res.status(200).json({ message: 'Test email sent successfully!' });

  } catch (error) {
    console.error('💥 Test email error:', error);
    res.status(500).json({ error: 'Test email failed' });
  }
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Serve React app for all non-API routes
app.get('*', (req, res) => {
  // Don't serve React app for API routes
  if (req.path.startsWith('/api')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  // Serve React app for all other routes
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

const PORT = process.env.PORT || 3001;

// Graceful shutdown
const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📧 Email recipients configured: ${getEmailRecipients().length} recipient(s)`);
  console.log(`🔒 Security features enabled: CORS, Rate Limiting, Helmet`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
