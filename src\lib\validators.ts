// Form validation utilities
import type { ContactFormData, ContactFormErrors } from '../types';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation regex (international format, allows numbers starting with 0)
const PHONE_REGEX = /^[+]?[0-9][\d\s\-()]{5,19}$/;

// Name validation regex (letters, spaces, hyphens, apostrophes)
const NAME_REGEX = /^[a-zA-ZÀ-ÿ\s\-']+$/;

/**
 * Validate email address
 */
export function validateEmail(email: string): boolean {
  return EMAIL_REGEX.test(email.trim());
}

/**
 * Validate phone number
 */
export function validatePhone(phone: string): boolean {
  if (!phone.trim()) return true; // Phone is optional
  return PHONE_REGEX.test(phone.trim());
}

/**
 * Validate name (first name, last name, company name)
 */
export function validateName(name: string): boolean {
  return name.trim().length >= 2 && NAME_REGEX.test(name.trim());
}

/**
 * Validate message length
 */
export function validateMessage(message: string, minLength = 10, maxLength = 1000): boolean {
  const trimmed = message.trim();
  return trimmed.length >= minLength && trimmed.length <= maxLength;
}

/**
 * Validate contact form data
 */
export function validateContactForm(data: ContactFormData): ContactFormErrors {
  const errors: ContactFormErrors = {};

  // Name validation
  if (!data.name || !validateName(data.name)) {
    errors.name = 'Please enter a valid name (at least 2 characters, letters only)';
  }

  // Email validation
  if (!data.email || !validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Phone validation (optional)
  if (data.phone && data.phone.trim().length > 0 && !validatePhone(data.phone)) {
    errors.phone = 'Please enter a valid phone number';
  }

  // Company validation (optional)
  if (data.company && data.company.trim().length > 0 && data.company.trim().length < 2) {
    errors.company = 'Company name must be at least 2 characters';
  }

  // Message validation
  if (!data.message || !validateMessage(data.message)) {
    errors.message = 'Please enter a message between 10 and 1000 characters';
  }

  // Service validation
  if (!data.service || data.service.trim().length === 0) {
    errors.service = 'Please select a service';
  }

  return errors;
}

/**
 * Check if form has any validation errors
 */
export function hasValidationErrors(errors: ContactFormErrors): boolean {
  return Object.keys(errors).length > 0;
}

/**
 * Sanitize input string
 */
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000); // Limit length
}

/**
 * Sanitize contact form data
 */
export function sanitizeContactForm(data: ContactFormData): ContactFormData {
  return {
    name: sanitizeInput(data.name),
    email: sanitizeInput(data.email),
    phone: data.phone ? sanitizeInput(data.phone) : undefined,
    company: data.company ? sanitizeInput(data.company) : undefined,
    message: sanitizeInput(data.message),
    service: data.service ? sanitizeInput(data.service) : undefined,
  };
}

/**
 * Validate and sanitize contact form
 */
export function processContactForm(data: ContactFormData): {
  sanitizedData: ContactFormData;
  errors: ContactFormErrors;
  isValid: boolean;
} {
  const sanitizedData = sanitizeContactForm(data);
  const errors = validateContactForm(sanitizedData);
  const isValid = !hasValidationErrors(errors);

  return {
    sanitizedData,
    errors,
    isValid,
  };
}

/**
 * Newsletter email validation
 */
export function validateNewsletterEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  const trimmedEmail = email.trim();

  if (!trimmedEmail) {
    return {
      isValid: false,
      error: 'Email address is required',
    };
  }

  if (!validateEmail(trimmedEmail)) {
    return {
      isValid: false,
      error: 'Please enter a valid email address',
    };
  }

  return {
    isValid: true,
  };
}

/**
 * Generic field validator
 */
export function validateField(
  value: string,
  rules: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: string) => boolean;
  }
): string | null {
  const trimmed = value.trim();

  if (rules.required && !trimmed) {
    return 'This field is required';
  }

  if (rules.minLength && trimmed.length < rules.minLength) {
    return `Minimum length is ${rules.minLength} characters`;
  }

  if (rules.maxLength && trimmed.length > rules.maxLength) {
    return `Maximum length is ${rules.maxLength} characters`;
  }

  if (rules.pattern && !rules.pattern.test(trimmed)) {
    return 'Invalid format';
  }

  if (rules.custom && !rules.custom(trimmed)) {
    return 'Invalid value';
  }

  return null;
}
