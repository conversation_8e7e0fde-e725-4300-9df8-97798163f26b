import React from 'react';

interface StatCardProps {
  value: string;
  label: string;
}

const StatCard: React.FC<StatCardProps> = ({ value, label }) => {
  return (
    <div className="text-center p-6 bg-blue-800 dark:bg-gray-700 rounded-lg transition-transform hover:scale-105 border border-blue-700 dark:border-gray-600">
      <div className="text-4xl md:text-5xl font-bold text-orange-400 dark:text-orange-300 mb-2">{value}</div>
      <div className="text-lg text-blue-100 dark:text-gray-200">{label}</div>
    </div>
  );
};

export default StatCard;