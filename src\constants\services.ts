import { Users, Shield, Scale, CheckCircle, FileText, HardHat, Atom, Flame } from 'lucide-react';
import React from 'react';

// Service Configuration Interface
export interface ServiceConfig {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
}

// Centralized Services Configuration
export const SERVICES_CONFIG: ServiceConfig[] = [
  { id: 'training', icon: Users },
  { id: 'consulting', icon: Shield },
  { id: 'audits', icon: CheckCircle },
  { id: 'compliance', icon: Scale },
  { id: 'reporting', icon: FileText },
  { id: 'mining-safety', icon: HardHat },
  { id: 'sds', icon: Atom },
  { id: 'emergency', icon: Flame }
];

// Core Services (used in main navigation and homepage)
export const CORE_SERVICES = SERVICES_CONFIG.slice(0, 4);

// All Services (used in services page)
export const ALL_SERVICES = SERVICES_CONFIG;

// Service Helper Functions
export const getServiceById = (id: string): ServiceConfig | undefined => {
  return SERVICES_CONFIG.find(service => service.id === id);
};

export const getServiceIcon = (id: string, className: string = "w-10 h-10"): React.ReactElement | null => {
  const service = getServiceById(id);
  if (!service) return null;

  const IconComponent = service.icon;
  return React.createElement(IconComponent, {
    className,
    style: { color: 'rgb(var(--color-secondary-500))' }
  });
};

// Generate service links with language support
export const getServiceLink = (serviceId: string, currentLang: string): string => {
  return `/${currentLang}/services/${serviceId}`;
};

// Generate service links for navigation
export const getServiceLinks = (currentLang: string) => {
  return SERVICES_CONFIG.map(service => ({
    id: service.id,
    link: getServiceLink(service.id, currentLang)
  }));
};