// This interface now defines the shape of the non-translatable data
export interface BlogPostData {
  id: string;
  image: string;
  date: string;
  author: string;
  authorTitleKey: string; // Translation key for author title
  tags?: string[];
  readTimeMinutes: number; // Just the number, suffix comes from translation
}

// This array is now the single source of truth for which blog posts exist.
export const blogPostsData: BlogPostData[] = [
  {
    id: 'post1',
    image: '/images/blog-post-1.jpg',
    date: '15 Maj 2025',
    author: 'author_name', // Translation key
    authorTitleKey: 'author_title',
    tags: ['safety-culture', 'leadership', 'employee-engagement', 'training'],
    readTimeMinutes: 8
  },
  {
    id: 'post6',
    image: '/images/blog-post-6.jpg',
    date: '20 Prill 2025',
    author: 'author_name',
    authorTitle<PERSON>ey: 'author_title',
    tags: ['ligji-04-L-161', 'kosove', 'siguria-ne-pune', 'perputhshmeria-ligjore', 'legjislacioni-i-punes', 'sshp'],
    readTimeMinutes: 8
  },
  {
    id: 'post3',
    image: '/images/blog-post-3.jpg',
    date: '5 Mars 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['safety-culture', 'leadership', 'training', 'best-practices'],
    readTimeMinutes: 7
  },
  {
    id: 'post4',
    image: '/images/blog-post-4.jpg',
    date: '20 Mars 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['ergonomics', 'well-being', 'productivity', 'office-health', 'injury-prevention'],
    readTimeMinutes: 7
  },
  {
    id: 'post5',
    image: '/images/blog-post-5-mental-health.jpg',
    date: '5 Prill 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['mental-health', 'well-being', 'workplace-culture', 'stress-management', 'employee-support'],
    readTimeMinutes: 9
  },
  {
    id: 'post2',
    image: '/images/blog-post-2.jpg',
    date: '10 Mars 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['technology', 'ai', 'innovation', 'safety-management'],
    readTimeMinutes: 6
  },
  {
    id: 'post7',
    image: '/images/blog-post-7-emergency-planning.jpg',
    date: '5 Maj 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['planifikim-emergjence', 'reagim-ne-kriza', 'evakuim', 'siguria-ne-pune', 'vazhdimesia-e-biznesit', 'menaxhim-krizash'],
    readTimeMinutes: 7
  },
  {
    id: 'post8',
    image: '/images/blog-post-3.jpg',
    date: '20 Maj 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['keshilla-sigurie', 'siguria-ne-pune', 'parandalimi', 'praktika-te-mira', 'ndërgjegjësimi', 'ppm'],
    readTimeMinutes: 6
  },
  {
    id: 'post9',
    image: '/images/blog-post-9.jpg',
    date: '30 Gusht 2025',
    author: 'author_name',
    authorTitleKey: 'author_title',
    tags: ['gabimet-sigurie', 'bizneset-kosovare', 'ligji-04-L-161', 'siguria-ne-pune', 'kosove', 'parandalimi', 'perputhshmeria'],
    readTimeMinutes: 8
  }
];