# Database Setup and Management

This document describes the database schema and management for the SafeWork-Kosova CRM system.

## Overview

The system uses SQLite for development and can be configured to use PostgreSQL for production. The database stores:

- **Inquiries**: User contact information and safety-related inquiries
- **Resources**: Educational materials, templates, and safety documents
- **User Activities**: Analytics data for resource usage and user interactions

## Database Schema

### Tables

#### inquiries
- `id` (TEXT PRIMARY KEY) - Unique identifier
- `contact_name` (TEXT NOT NULL) - User's full name
- `contact_email` (TEXT NOT NULL) - User's email address
- `company_name` (TEXT) - Optional company name
- `inquiry_category` (TEXT NOT NULL) - Category: risk-assessment, training, compliance, emergency, general
- `inquiry_subcategory` (TEXT) - Optional subcategory
- `urgency_level` (TEXT NOT NULL) - Level: low, medium, high, emergency
- `description` (TEXT NOT NULL) - Inquiry details
- `status` (TEXT DEFAULT 'new') - Status: new, in-progress, responded, closed, referred
- `assigned_to` (TEXT) - Optional staff assignment
- `requires_professional` (INTEGER DEFAULT 0) - Boolean flag for professional consultation need
- `created_at` (DATETIME) - Creation timestamp
- `updated_at` (DATETIME) - Last update timestamp

#### resources
- `id` (TEXT PRIMARY KEY) - Unique identifier
- `title` (TEXT NOT NULL) - Resource title
- `description` (TEXT) - Optional description
- `category` (TEXT NOT NULL) - Category: risk-assessment, training-materials, compliance-templates, safety-checklists, emergency-procedures, jsa-templates
- `type` (TEXT NOT NULL) - Type: template, guide, checklist, video, document, form
- `file_url` (TEXT NOT NULL) - File location
- `preview_url` (TEXT) - Optional preview image
- `file_size` (INTEGER) - File size in bytes
- `format` (TEXT) - File format (pdf, docx, etc.)
- `language` (TEXT DEFAULT 'sq') - Language: sq (Albanian) or en (English)
- `download_count` (INTEGER DEFAULT 0) - Download counter
- `disclaimer` (TEXT) - Legal disclaimer text
- `created_at` (DATETIME) - Creation timestamp
- `updated_at` (DATETIME) - Last update timestamp

#### user_activities
- `id` (TEXT PRIMARY KEY) - Unique identifier
- `session_id` (TEXT) - Optional session identifier
- `user_email` (TEXT) - Optional user email
- `ip_address` (TEXT) - Optional IP address
- `activity_type` (TEXT NOT NULL) - Type: resource_download, inquiry_submit, page_view, search
- `resource_id` (TEXT) - Optional resource reference (FK)
- `inquiry_id` (TEXT) - Optional inquiry reference (FK)
- `metadata` (TEXT) - Optional JSON metadata
- `timestamp` (DATETIME) - Activity timestamp

### Indexes

Performance indexes are created on frequently queried columns:
- `idx_inquiries_status` - Inquiry status
- `idx_inquiries_category` - Inquiry category
- `idx_inquiries_created_at` - Inquiry creation date
- `idx_resources_category` - Resource category
- `idx_resources_type` - Resource type
- `idx_user_activities_type` - Activity type
- `idx_user_activities_timestamp` - Activity timestamp

## Management Commands

### Migrations

Run database migrations:
```bash
npm run migrate up
```

Check migration status:
```bash
npm run migrate status
```

Rollback to specific version:
```bash
npm run migrate down <version>
```

### Seeding

Populate database with sample data for development:
```bash
npm run seed
```

## Development Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run migrations:
   ```bash
   npm run migrate up
   ```

3. Seed with sample data (optional):
   ```bash
   npm run seed
   ```

4. Run tests:
   ```bash
   npm run test:run src/test/database.test.ts
   ```

## Database Location

- **Development**: `./data/crm.db`
- **Production**: `/tmp/crm.db` (or configured path)

## TypeScript Interfaces

The database operations use strongly-typed interfaces defined in `src/types/database.ts`:

- `Inquiry` - Main inquiry object
- `Resource` - Resource object
- `UserActivity` - User activity object
- `InquiryRow`, `ResourceRow`, `UserActivityRow` - Database row interfaces

## Utility Functions

Database utility functions are available in `src/lib/database.ts`:

- `getDatabase()` - Get database connection
- `closeDatabase()` - Close database connection
- `generateId()` - Generate UUID for records
- `rowToInquiry()`, `inquiryToRow()` - Convert between objects and database rows
- `rowToResource()`, `resourceToRow()` - Convert between objects and database rows
- `rowToUserActivity()`, `userActivityToRow()` - Convert between objects and database rows

## Compliance Notes

The database design supports Kosovo law compliance requirements:

1. **Data Minimization**: Only essential contact information is stored
2. **Audit Trail**: All data access and modifications are logged
3. **Privacy**: User activities can be tracked anonymously
4. **Disclaimers**: Resources include legal disclaimers
5. **Professional Referrals**: System tracks when professional consultation is needed

## Testing

Comprehensive unit tests cover:
- Database connection management
- Data model conversions
- CRUD operations
- Migration system
- Schema validation
- Foreign key constraints

Run tests with:
```bash
npm run test:run src/test/database.test.ts
```