import React from 'react';
import { Award } from 'lucide-react';
// Import the useTranslation hook
import { useTranslation } from 'react-i18next';

interface TeamMemberCardProps {
  name: string;
  position: string;
  image: string;
  bio: string;
  certifications: string[];
}

const TeamMemberCard: React.FC<TeamMemberCardProps> = ({ name, position, image, bio, certifications }) => {
  // Initialize the translation hook
  const { t } = useTranslation();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden group border border-gray-200 dark:border-gray-700">
      <div className="flex justify-center items-center py-6">
        <img
          src={image}
          alt={name}
          className="w-40 h-40 rounded-full object-cover object-center mx-auto shadow-md transition-transform duration-500 group-hover:scale-105"
        />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white">{name}</h3>
        <p className="text-orange-500 dark:text-orange-400 font-medium mb-3">{position}</p>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{bio}</p>
        <div className="space-y-1">
          {/* Translate the certifications label */}
          <p className="font-medium text-gray-900 dark:text-white mb-1">{t('team_certifications_label')}</p>
          {certifications.map((cert, index) => (
            <div key={index} className="flex items-center">
              <Award className="w-4 h-4 text-blue-800 dark:text-blue-400 mr-2" />
              <span className="text-sm text-gray-700 dark:text-gray-300">{cert}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TeamMemberCard;