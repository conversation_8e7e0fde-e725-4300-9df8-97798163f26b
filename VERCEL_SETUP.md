# Vercel Deployment Setup for SafeWork Kosova

## 🚀 Quick Setup Guide

Your website is now deployed on Vercel with serverless API functions! Here's how to complete the setup:

### 1. Set Up Environment Variables in Vercel

1. **Go to your Vercel dashboard**: https://vercel.com/dashboard
2. **Select your project**: SafeWork-Kosova
3. **Go to Settings → Environment Variables**
4. **Add these variables:**

```
RESEND_API_KEY = your_resend_api_key_here
EMAIL_RECIPIENTS = <EMAIL>,<EMAIL>
```

### 2. Get a Resend API Key

1. **Sign up at Resend**: https://resend.com
2. **Go to API Keys**: https://resend.com/api-keys
3. **Create a new API key**
4. **Copy the key** and add it to Vercel environment variables

### 3. Test Your API Endpoints

After setting up the environment variables, test these endpoints:

- **Health Check**: `https://your-domain.vercel.app/api/health`
- **Contact Form**: `https://your-domain.vercel.app/api/contact` (POST)
- **Newsletter**: `https://your-domain.vercel.app/api/subscribe` (POST)

### 4. Redeploy

After adding environment variables:
1. **Go to Deployments tab** in Vercel
2. **Click the three dots** on the latest deployment
3. **Select "Redeploy"**

## 📁 File Structure

```
/
├── api/                    # Vercel serverless functions
│   ├── contact.js         # Contact form handler
│   ├── subscribe.js       # Newsletter subscription
│   ├── health.js          # Health check endpoint
│   └── package.json       # API dependencies
├── dist/                  # Built static files
├── src/                   # Source code
├── vercel.json           # Vercel configuration
└── .env.example          # Environment variables template
```

## 🔧 API Endpoints

### POST /api/contact
Handles contact form submissions.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "company": "Example Corp",
  "service": "Safety Training",
  "message": "I need help with workplace safety training."
}
```

### POST /api/subscribe
Handles newsletter subscriptions.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### GET /api/health
Returns API health status.

## 🛠️ Troubleshooting

### Contact Form Not Working?
1. **Check environment variables** are set in Vercel
2. **Verify Resend API key** is valid
3. **Check browser console** for error messages
4. **Test health endpoint**: `/api/health`

### Newsletter Not Working?
1. **Same steps as contact form**
2. **Check email format** is valid
3. **Verify rate limiting** isn't blocking requests

### Common Issues:
- **"Email service not configured"**: Missing `RESEND_API_KEY`
- **"Failed to send email"**: Invalid Resend API key
- **CORS errors**: Should be handled automatically
- **Rate limiting**: Wait an hour or use different IP

## 📧 Email Configuration

### Default Recipients
If not specified, emails go to: `<EMAIL>`

### Multiple Recipients
Set `EMAIL_RECIPIENTS` with comma-separated emails:
```
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
```

### Email Templates
- **Contact Form**: Professional table format with all form data
- **Newsletter**: Welcome email sent to subscriber + admin notification

## 🔒 Security Features

- **Rate Limiting**: 5 contact forms, 10 subscriptions per hour per IP
- **Input Validation**: Email format, required fields, length limits
- **HTML Escaping**: Prevents XSS attacks
- **CORS Headers**: Properly configured for web requests

## 🚀 Next Steps

1. **Set up custom domain** in Vercel (optional)
2. **Configure DNS** to point to Vercel
3. **Test all functionality** thoroughly
4. **Monitor logs** in Vercel dashboard

## 📞 Support

If you encounter issues:
1. **Check Vercel logs** in the dashboard
2. **Verify environment variables** are set correctly
3. **Test API endpoints** individually
4. **Check browser network tab** for detailed error messages

Your website should now have fully functional contact forms and newsletter subscription!
