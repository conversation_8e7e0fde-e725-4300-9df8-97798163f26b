import { Resend } from 'resend';
import type { Inquiry } from '../types/database.js';

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * HTML escape function for security
 */
function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, function (m) { return map[m]; });
}

/**
 * Get email recipients from environment
 */
function getEmailRecipients(): string[] {
  const recipients = process.env.EMAIL_RECIPIENTS || '<EMAIL>';
  return recipients.split(',').map(email => email.trim());
}

/**
 * Get category display name
 */
function getCategoryDisplayName(category: string, language: 'en' | 'sq' = 'en'): string {
  const categoryNames = {
    'risk-assessment': {
      'en': 'Risk Assessment',
      'sq': 'Vlerësimi i Rrezikut'
    },
    'training': {
      'en': 'Safety Training',
      'sq': 'Trajnime Sigurie'
    },
    'compliance': {
      'en': 'Regulatory Compliance',
      'sq': 'Përputhshmëria me Rregulloret'
    },
    'emergency': {
      'en': 'Emergency Response',
      'sq': 'Përgjigja e Emergjencës'
    },
    'general': {
      'en': 'General Inquiry',
      'sq': 'Kërkesë e Përgjithshme'
    }
  };

  return categoryNames[category as keyof typeof categoryNames]?.[language] || category;
}

/**
 * Get urgency level display name
 */
function getUrgencyDisplayName(urgency: string, language: 'en' | 'sq' = 'en'): string {
  const urgencyNames = {
    'low': {
      'en': 'Low Priority',
      'sq': 'Prioritet i Ulët'
    },
    'medium': {
      'en': 'Medium Priority',
      'sq': 'Prioritet i Mesëm'
    },
    'high': {
      'en': 'High Priority',
      'sq': 'Prioritet i Lartë'
    },
    'emergency': {
      'en': 'Emergency',
      'sq': 'Emergjencë'
    }
  };

  return urgencyNames[urgency as keyof typeof urgencyNames]?.[language] || urgency;
}

/**
 * Get urgency color for email styling
 */
function getUrgencyColor(urgency: string): string {
  const colors = {
    'low': '#10b981',
    'medium': '#f59e0b',
    'high': '#ef4444',
    'emergency': '#dc2626'
  };

  return colors[urgency as keyof typeof colors] || '#6b7280';
}

/**
 * Send inquiry notification email to admin
 */
export async function sendInquiryNotification(inquiry: Inquiry, clientIP?: string): Promise<void> {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('Resend API key not configured');
  }

  const recipients = getEmailRecipients();
  const categoryDisplay = getCategoryDisplayName(inquiry.inquiryCategory);
  const urgencyDisplay = getUrgencyDisplayName(inquiry.urgencyLevel);
  const urgencyColor = getUrgencyColor(inquiry.urgencyLevel);

  // Escape all user input for safe HTML rendering
  const safeData = {
    id: escapeHtml(inquiry.id),
    contactName: escapeHtml(inquiry.contactName),
    contactEmail: escapeHtml(inquiry.contactEmail),
    companyName: escapeHtml(inquiry.companyName || 'Not provided'),
    category: escapeHtml(inquiry.inquiryCategory),
    categoryDisplay: escapeHtml(categoryDisplay),
    subcategory: escapeHtml(inquiry.inquirySubcategory || 'Not specified'),
    urgencyLevel: escapeHtml(inquiry.urgencyLevel),
    urgencyDisplay: escapeHtml(urgencyDisplay),
    description: escapeHtml(inquiry.description).replace(/\n/g, '<br>'),
    requiresProfessional: inquiry.requiresProfessional ? 'Yes' : 'No',
    createdAt: inquiry.createdAt.toLocaleString(),
    clientIP: escapeHtml(clientIP || 'Unknown')
  };

  const { error } = await resend.emails.send({
    from: 'CRM System <<EMAIL>>',
    to: recipients,
    subject: `New CRM Inquiry - ${safeData.categoryDisplay} (${safeData.urgencyDisplay})`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New CRM Inquiry</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 30px 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">SafeWork Kosova CRM</h1>
            <p style="color: #dbeafe; margin: 10px 0 0 0; font-size: 16px;">New Inquiry Received</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 30px 20px;">
            <div style="background-color: ${urgencyColor === '#dc2626' ? '#fef2f2' : '#fef3c7'}; border-left: 4px solid ${urgencyColor}; padding: 15px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
              <p style="margin: 0; color: ${urgencyColor === '#dc2626' ? '#991b1b' : '#92400e'}; font-weight: 600;">
                🔔 ${safeData.urgencyDisplay} inquiry for: ${safeData.categoryDisplay}
              </p>
            </div>
            
            <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 22px;">Inquiry Details</h2>
            
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px; background-color: #f9fafb; border-radius: 8px; overflow: hidden;">
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6; width: 30%;">🆔 ID:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937; font-family: monospace;">${safeData.id}</td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">👤 Name:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${safeData.contactName}</td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">📧 Email:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;"><a href="mailto:${safeData.contactEmail}" style="color: #2563eb; text-decoration: none;">${safeData.contactEmail}</a></td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">🏢 Company:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${safeData.companyName}</td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">📂 Category:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">
                  <span style="background-color: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500;">${safeData.categoryDisplay}</span>
                  ${safeData.subcategory !== 'Not specified' ? `<br><small style="color: #6b7280; font-size: 12px;">Subcategory: ${safeData.subcategory}</small>` : ''}
                </td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">⚡ Priority:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">
                  <span style="background-color: ${urgencyColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500;">${safeData.urgencyDisplay}</span>
                </td>
              </tr>
              <tr>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">🔧 Professional:</td>
                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">
                  <span style="background-color: ${inquiry.requiresProfessional ? '#dcfce7' : '#f3f4f6'}; color: ${inquiry.requiresProfessional ? '#166534' : '#6b7280'}; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500;">
                    ${safeData.requiresProfessional}
                  </span>
                </td>
              </tr>
              <tr>
                <td style="padding: 15px; font-weight: 600; color: #374151; background-color: #f3f4f6; vertical-align: top;">💬 Description:</td>
                <td style="padding: 15px; color: #1f2937; line-height: 1.6;">${safeData.description}</td>
              </tr>
            </table>
            
            <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
              <h3 style="margin: 0 0 10px 0; color: #0c4a6e; font-size: 16px;">📊 Submission Info</h3>
              <p style="margin: 0; color: #0c4a6e; font-size: 14px;">
                <strong>Submitted:</strong> ${safeData.createdAt}<br>
                <strong>IP Address:</strong> ${safeData.clientIP}<br>
                <strong>Status:</strong> New
              </p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <p style="color: #6b7280; margin: 0 0 15px 0; font-size: 16px;">Manage this inquiry in the CRM system:</p>
              <a href="https://safework-kosova.com/admin/inquiries/${safeData.id}" style="display: inline-block; background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">View in CRM</a>
            </div>
          </div>
          
          <!-- Footer -->
          <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #6b7280; font-size: 12px;">
              This is an automated notification from SafeWork Kosova CRM system.<br>
              Please respond to the customer directly at their email address.
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
  });

  if (error) {
    console.error('Failed to send inquiry notification:', error);
    throw new Error('Failed to send email notification');
  }
}

/**
 * Send confirmation email to user
 */
export async function sendInquiryConfirmation(inquiry: Inquiry, language: 'en' | 'sq' = 'en'): Promise<void> {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('Resend API key not configured');
  }

  const categoryDisplay = getCategoryDisplayName(inquiry.inquiryCategory, language);
  const urgencyDisplay = getUrgencyDisplayName(inquiry.urgencyLevel, language);

  // Escape user input
  const safeData = {
    contactName: escapeHtml(inquiry.contactName),
    contactEmail: escapeHtml(inquiry.contactEmail),
    categoryDisplay: escapeHtml(categoryDisplay),
    urgencyDisplay: escapeHtml(urgencyDisplay),
    description: escapeHtml(inquiry.description).replace(/\n/g, '<br>')
  };

  let subject = '';
  let htmlBody = '';

  if (language === 'sq') {
    subject = 'Faleminderit për kërkesën tuaj - SafeWork Kosova CRM';
    htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Konfirmimi i Kërkesës</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
            <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Platforma Edukative për Sigurinë në Punë</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 40px 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="background-color: #10b981; color: white; width: 60px; height: 60px; border-radius: 30px; margin: 0 auto 20px auto; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold;">✓</div>
              <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Faleminderit, ${safeData.contactName}!</h2>
              <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">Kemi pranuar kërkesën tuaj dhe do t'ju përgjigjemi së shpejti</p>
            </div>
            
            <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
              <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 Përmbledhja e Kërkesës Suaj</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151; width: 30%;">📂 Kategoria:</td>
                  <td style="padding: 10px 0; color: #1f2937;">
                    <span style="background-color: #dbeafe; color: #1e40af; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.categoryDisplay}</span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151;">⚡ Prioriteti:</td>
                  <td style="padding: 10px 0; color: #1f2937;">
                    <span style="background-color: #f59e0b; color: white; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.urgencyDisplay}</span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151; vertical-align: top;">💬 Përshkrimi:</td>
                  <td style="padding: 10px 0; color: #1f2937; line-height: 1.6;">${safeData.description}</td>
                </tr>
              </table>
            </div>
            
            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
              <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">⏱️ Çfarë Ndodh Tani?</h3>
              <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                <li style="margin-bottom: 8px;">Ekipi ynë do ta shqyrtojë kërkesën tuaj</li>
                <li style="margin-bottom: 8px;">Do t'ju kontaktojmë me burime edukative të përshtatshme</li>
                <li style="margin-bottom: 0;">Nëse nevojitet, do t'ju drejtojmë tek profesionistë të licencuar</li>
              </ul>
            </div>
            
            <div style="background-color: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; padding: 20px; margin: 25px 0;">
              <h3 style="margin: 0 0 10px 0; color: #dc2626; font-size: 16px;">⚠️ Njoftim i Rëndësishëm</h3>
              <p style="margin: 0; color: #dc2626; font-size: 14px; line-height: 1.6;">
                Ky është një sistem edukativ dhe nuk ofron shërbime konsulence të licencuara. 
                Për shërbime profesionale, ju lutemi kontaktoni konsulentë të licencuar të sigurisë në punë.
              </p>
            </div>
          </div>
          
          <!-- Footer -->
          <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #6b7280; font-size: 12px;">
              Ky është një konfirmim i automatizuar. Mos u përgjigjni këtij emaili.<br>
              Për pyetje, na shkruani në: <a href="mailto:<EMAIL>" style="color: #0ea5e9; text-decoration: none;"><EMAIL></a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  } else {
    subject = 'Thank you for your inquiry - SafeWork Kosova CRM';
    htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Inquiry Confirmation</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
            <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Educational Safety Platform</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 40px 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="background-color: #10b981; color: white; width: 60px; height: 60px; border-radius: 30px; margin: 0 auto 20px auto; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold;">✓</div>
              <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Thank You, ${safeData.contactName}!</h2>
              <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">We've received your inquiry and will respond soon</p>
            </div>
            
            <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
              <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 Your Inquiry Summary</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151; width: 30%;">📂 Category:</td>
                  <td style="padding: 10px 0; color: #1f2937;">
                    <span style="background-color: #dbeafe; color: #1e40af; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.categoryDisplay}</span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151;">⚡ Priority:</td>
                  <td style="padding: 10px 0; color: #1f2937;">
                    <span style="background-color: #f59e0b; color: white; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.urgencyDisplay}</span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 10px 0; font-weight: 600; color: #374151; vertical-align: top;">💬 Description:</td>
                  <td style="padding: 10px 0; color: #1f2937; line-height: 1.6;">${safeData.description}</td>
                </tr>
              </table>
            </div>
            
            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
              <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">⏱️ What Happens Next?</h3>
              <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                <li style="margin-bottom: 8px;">Our team will review your inquiry</li>
                <li style="margin-bottom: 8px;">We'll contact you with appropriate educational resources</li>
                <li style="margin-bottom: 0;">If needed, we'll guide you to licensed professionals</li>
              </ul>
            </div>
            
            <div style="background-color: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; padding: 20px; margin: 25px 0;">
              <h3 style="margin: 0 0 10px 0; color: #dc2626; font-size: 16px;">⚠️ Important Notice</h3>
              <p style="margin: 0; color: #dc2626; font-size: 14px; line-height: 1.6;">
                This is an educational platform and does not provide licensed consulting services. 
                For professional services, please contact licensed workplace safety consultants.
              </p>
            </div>
          </div>
          
          <!-- Footer -->
          <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #6b7280; font-size: 12px;">
              This is an automated confirmation. Please do not reply to this email.<br>
              For questions, contact us at: <a href="mailto:<EMAIL>" style="color: #0ea5e9; text-decoration: none;"><EMAIL></a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  const { error } = await resend.emails.send({
    from: 'SafeWork Kosova <<EMAIL>>',
    to: [inquiry.contactEmail],
    subject,
    html: htmlBody,
  });

  if (error) {
    console.error('Failed to send inquiry confirmation:', error);
    // Don't throw error for confirmation emails - they're not critical
  }
}