# Project: SafeWorkKosova

## Project Overview

This project is a professional workplace safety consulting website for SafeWork Kosova. It is built with a modern tech stack, including React, TypeScript, Vite, and Express.js. The website is designed to be multilingual (Albanian/English), responsive, and accessible. It features a blog, case studies, and safety resources, as well as contact and newsletter forms that integrate with the Resend API for email delivery. The project is also SEO-optimized with Schema.org structured data and includes a sitemap for better search engine visibility.

## Building and Running

### Prerequisites

- Node.js (v18 or newer recommended)
- npm or pnpm

### Installation

To install the project dependencies, run one of the following commands:

```bash
npm install
```

or

```bash
pnpm install
```

### Development

To run the project in development mode, which includes both the Vite frontend and the Express backend, use the following command:

```bash
npm run dev:both
```

This will start the Vite development server on port 5173 and the Express server on port 3000.

### Building for Production

To build the project for production, run the following command:

```bash
npm run build
```

This will create a `dist` directory with the optimized and minified production build.

### Testing

To run the project's tests, use the following command:

```bash
npm run test
```

## Development Conventions

### Coding Style

The project uses ESLint to enforce a consistent coding style. To check for linting errors, run the following command:

```bash
npm run lint
```

### Testing Practices

The project uses Vitest for testing. Test files are located in the `src/test` directory and follow the `*.test.ts` or `*.test.tsx` naming convention.

### Contribution Guidelines

While there are no explicit contribution guidelines in the project, the use of ESLint and a clear project structure suggests that contributions should follow the existing coding style and conventions.
