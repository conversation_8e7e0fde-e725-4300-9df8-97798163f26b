import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

interface HeroProps {
  title: string;
  subtitle: string;
  ctaText?: string;
  ctaLink?: string;
  bgImage?: string;
}

const Hero: React.FC<HeroProps> = ({
  title,
  subtitle,
  ctaText,
  ctaLink,
  bgImage = "/images/hero-default.jpg"
}) => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  
  // Use the ctaLink as-is if provided, otherwise use default with language prefix
  const finalCtaLink = ctaLink || `/${currentLang}/contact#formular-kontakti`;
  
  return (
    <section className="relative">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 flex justify-center items-center overflow-hidden pointer-events-none">
        <img 
          src={bgImage} 
          alt="Hero background" 
          className="w-full h-full object-cover brightness-90" 
          style={{ maxHeight: 600 }}
        />
        <div className="absolute inset-0 bg-blue-900/60 dark:bg-blue-950/70"></div>
      </div>
      
      {/* Content */}
      <div className="relative min-h-[600px] flex items-center z-10">
        <div className="container mx-auto px-4 md:px-6 py-20 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              {title}
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8">
              {subtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 relative z-20">
              {ctaText && (
                <Link 
                  to={finalCtaLink}
                  className="btn-primary-lg relative z-30"
                >
                  {ctaText}
                </Link>
              )}
              <Link 
                to={`/${currentLang}/services`} 
                className="btn-secondary-lg relative z-30"
              >
                {t('common:our_services')}
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      {/* Wave SVG Divider */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto fill-white dark:fill-gray-900 transition-colors duration-300">
          <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default Hero;


