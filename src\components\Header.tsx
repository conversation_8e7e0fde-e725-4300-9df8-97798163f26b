import React, { useState, useEffect } from 'react';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Menu, X, ChevronDown, Phone } from 'lucide-react';
// Logo is now served from public folder
const SafeWorkKosovaLogo = '/images/SafeWorkKosova.png';
import ThemeToggle from './ThemeToggle';
import LanguageSwitcher from './LanguageSwitcher';
import { getServiceLinks } from '../constants/services';
import { CONTACT_INFO } from '../constants/common';

const Header: React.FC = () => {
  const { t, i18n } = useTranslation(['common', 'services']);
  const currentLang = i18n.language;

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [servicesDropdown, setServicesDropdown] = useState(false);
  const location = useLocation();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    closeMenu();
    setServicesDropdown(false);
  }, [location]);

  const navLinkClasses = ({ isActive }: { isActive: boolean }) =>
    `relative px-3 py-2 transition-all duration-200 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 rounded-md ${
      isActive
        ? 'font-medium text-orange-600 dark:text-orange-400'
        : 'hover:bg-blue-50 hover:text-blue-700 hover:shadow-sm dark:hover:bg-gray-800 dark:hover:text-blue-300'
    }`;
    
  // Use shared services configuration
  const services = getServiceLinks(currentLang);

  return (
    <header
      id="navigation"
      role="banner"
      className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        isScrolled
          ? 'bg-white dark:bg-gray-900 shadow-md py-2'
          : 'bg-transparent dark:bg-gray-900 py-4'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        {/* --- DESKTOP HEADER --- */}
        <div className="hidden md:flex items-center">
          <div className="flex-shrink-0">
            <Link to={`/${currentLang}`} className="flex items-center">
              <img src={SafeWorkKosovaLogo} alt="SafeWork Kosova Logo" className="h-16 w-auto" />
              <span className="ml-2 text-xl font-bold text-blue-900">SafeWork Kosova</span>
            </Link>
          </div>

          {/* Right Group: Contains BOTH Nav and Actions, pushed to the right */}
          {/* FIX #1: Reduced gap from gap-x-10 to gap-x-6 to create more space for nav items. */}
          <div className="flex items-center ml-auto gap-x-8 pl-8">
            
            <nav role="navigation" aria-label="Main navigation" className="flex items-center space-x-1">
              <NavLink 
                to={`/${currentLang}`} 
                className={navLinkClasses} 
                style={{ color: 'rgb(var(--color-secondary-500))' }}
                end
              >
                {t('common:nav_home')}
              </NavLink>
              <NavLink 
                to={`/${currentLang}/about`} 
                className={navLinkClasses}
                style={{ color: 'rgb(var(--color-secondary-500))' }}
              >
                {t('common:nav_about')}
              </NavLink>
              <div className="relative group">
                <NavLink 
                  to={`/${currentLang}/services`} 
                  className={navLinkClasses}
                  style={{ color: 'rgb(var(--color-secondary-500))' }}
                  aria-expanded="false"
                  aria-haspopup="true"
                >
                  {t('common:nav_services')} <ChevronDown className="ml-1 w-4 h-4 inline" aria-hidden="true" />
                </NavLink>
                <div 
                  className="absolute left-0 mt-2 w-56 shadow-lg rounded-md overflow-hidden transform scale-0 group-hover:scale-100 opacity-0 group-hover:opacity-100 origin-top transition-all duration-200 z-10"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))',
                    border: '1px solid'
                  }}
                  role="menu"
                  aria-label="Services submenu"
                >
                  {services.map(service => (
                    <Link
                      key={service.id}
                      to={service.link}
                      className="block px-4 py-2 transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-orange-500"
                      style={{
                        color: 'rgb(var(--color-text-secondary))'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgb(var(--color-surface-hover))';
                        e.currentTarget.style.color = 'rgb(var(--color-primary-500))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = 'rgb(var(--color-text-secondary))';
                      }}
                      role="menuitem"
                    >
                      {t(`services:${service.id}.title`)}
                    </Link>
                  ))}
                </div>
              </div>
              <NavLink 
                to={`/${currentLang}/case-studies`} 
                className={navLinkClasses}
                style={{ color: 'rgb(var(--color-secondary-500))' }}
              >
                {t('common:nav_case_studies')}
              </NavLink>
              <NavLink 
                to={`/${currentLang}/blog`} 
                className={navLinkClasses}
                style={{ color: 'rgb(var(--color-secondary-500))' }}
              >
                {t('common:nav_blog')}
              </NavLink>
              <NavLink 
                to={`/${currentLang}/contact`} 
                className={navLinkClasses}
                style={{ color: 'rgb(var(--color-secondary-500))' }}
              >
                {t('common:nav_contact')}
              </NavLink>
            </nav>

            <div className="flex items-center gap-x-4">
              <a 
                href={`tel:${CONTACT_INFO.phone}`} 
                className="flex items-center font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md px-2 py-1"
                style={{ color: 'rgb(var(--color-secondary-500))' }}
                aria-label={`Call ${CONTACT_INFO.phone}`}
              >
                <Phone className="w-4 h-4 mr-1" aria-hidden="true" />{CONTACT_INFO.phone}
              </a>
              <Link to={`/${currentLang}/contact#formular-kontakti`} className="btn-primary whitespace-nowrap">{t('common:header_cta')}</Link>
              <ThemeToggle size="md" />
              <LanguageSwitcher />
            </div>
          </div>
        </div>

        {/* --- MOBILE HEADER --- */}
        <div className="md:hidden flex items-center justify-between">
            <Link to={`/${currentLang}`} className="flex items-center">
                <img src={SafeWorkKosovaLogo} alt="SafeWork Kosova Logo" className="h-16 w-auto" />
                <span className="ml-2 text-xl font-bold text-blue-900">SafeWork Kosova</span>
            </Link>
            <div className="flex items-center gap-4">
                <ThemeToggle size="md" />
                <LanguageSwitcher />
                <button 
                    className="focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 rounded-md p-1"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                    onClick={toggleMenu}
                    aria-expanded={isMenuOpen}
                    aria-controls="mobile-menu"
                    aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
                >
                    {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                </button>
            </div>
        </div>
        
        {/* Mobile Menu Content */}
        <div 
          id="mobile-menu"
          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMenuOpen ? 'max-h-screen opacity-100 py-4' : 'max-h-0 opacity-0'}`}
          aria-hidden={!isMenuOpen}
        >
          <nav 
            className="flex flex-col space-y-3 rounded-lg shadow-lg p-4"
            style={{
              backgroundColor: 'rgb(var(--color-surface))',
              borderColor: 'rgb(var(--color-border))',
              border: '1px solid'
            }}
            role="navigation"
            aria-label="Mobile navigation"
          >
            <NavLink 
              to={`/${currentLang}`} 
              className="px-1 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
              style={{ color: 'rgb(var(--color-text-secondary))' }}
              end
            >
              {t('common:nav_home')}
            </NavLink>
            <NavLink 
              to={`/${currentLang}/about`} 
              className="px-1 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
              style={{ color: 'rgb(var(--color-text-secondary))' }}
            >
              {t('common:nav_about')}
            </NavLink>
            <div>
              <div className="flex items-center justify-between w-full px-1 py-2">
                <NavLink 
                  to={`/${currentLang}/services`} 
                  className="rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                  style={{ color: 'rgb(var(--color-text-secondary))' }}
                >
                  {t('common:nav_services')}
                </NavLink>
                <button 
                  className="ml-2 p-1 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500" 
                  onClick={(e) => {e.stopPropagation(); setServicesDropdown(!servicesDropdown);}}
                  aria-expanded={servicesDropdown}
                  aria-controls="mobile-services-menu"
                  aria-label={servicesDropdown ? 'Collapse services menu' : 'Expand services menu'}
                  style={{ color: 'rgb(var(--color-text-secondary))' }}
                >
                  <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${servicesDropdown ? 'rotate-180' : ''}`} />
                </button>
              </div>
              <div 
                id="mobile-services-menu"
                className={`pl-4 space-y-2 overflow-hidden transition-all duration-300 ${servicesDropdown ? 'max-h-screen opacity-100 py-2' : 'max-h-0 opacity-0'}`}
                aria-hidden={!servicesDropdown}
              > 
                 {services.map(service => (
                   <Link 
                     key={service.id} 
                     to={service.link} 
                     className="block py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                     style={{ color: 'rgb(var(--color-text-muted))' }}
                   >
                     {t(`services:${service.id}.title`)}
                   </Link>
                 ))}
              </div>
            </div>
            <NavLink 
              to={`/${currentLang}/case-studies`} 
              className="px-1 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
              style={{ color: 'rgb(var(--color-text-secondary))' }}
            >
              {t('common:nav_case_studies')}
            </NavLink>
            <NavLink 
              to={`/${currentLang}/blog`} 
              className="px-1 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
              style={{ color: 'rgb(var(--color-text-secondary))' }}
            >
              {t('common:nav_blog')}
            </NavLink>
            <NavLink 
              to={`/${currentLang}/contact`} 
              className="px-1 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
              style={{ color: 'rgb(var(--color-text-secondary))' }}
            >
              {t('common:nav_contact')}
            </NavLink>
          </nav>
          <div 
            className="mt-4 pt-4"
            style={{ borderTopColor: 'rgb(var(--color-border))', borderTopWidth: '1px' }}
          >
            <a 
              href={`tel:${CONTACT_INFO.phone}`} 
              className="flex items-center font-medium mb-3 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md p-1"
              style={{ color: 'rgb(var(--color-secondary-500))' }}
              aria-label={`Call ${CONTACT_INFO.phone}`}
            >
              <Phone className="w-4 h-4 mr-2" aria-hidden="true" />{CONTACT_INFO.phone}
            </a>
            <Link to={`/${currentLang}/contact#formular-kontakti`} className="btn-primary w-full text-center block">{t('common:header_cta')}</Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;