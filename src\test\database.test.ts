import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  getDatabase, 
  closeDatabase, 
  generateId,
  rowToInquiry,
  inquiryToRow,
  rowToResource,
  resourceToRow,
  rowToUserActivity,
  userActivityToRow
} from '../lib/database.js';
import { runMigrations, rollbackMigrations, getMigrationStatus } from '../lib/migrations.js';
import type { Inquiry, Resource, UserActivity } from '../types/database.js';

describe('Database Operations', () => {
  beforeEach(() => {
    // Use in-memory database for tests
    process.env.NODE_ENV = 'test';
    runMigrations();
  });

  afterEach(() => {
    closeDatabase();
  });

  describe('Database Connection', () => {
    it('should create database connection', () => {
      const db = getDatabase();
      expect(db).toBeDefined();
    });

    it('should return same instance on multiple calls', () => {
      const db1 = getDatabase();
      const db2 = getDatabase();
      expect(db1).toBe(db2);
    });
  });

  describe('ID Generation', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });
  });

  describe('Inquiry Operations', () => {
    const sampleInquiry: Inquiry = {
      id: generateId(),
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      companyName: 'Test Company',
      inquiryCategory: 'risk-assessment',
      inquirySubcategory: 'workplace-hazards',
      urgencyLevel: 'medium',
      description: 'Need help with workplace risk assessment',
      status: 'new',
      assignedTo: undefined,
      requiresProfessional: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('should convert inquiry to row and back', () => {
      const row = inquiryToRow(sampleInquiry);
      expect(row.contact_name).toBe(sampleInquiry.contactName);
      expect(row.contact_email).toBe(sampleInquiry.contactEmail);
      expect(row.requires_professional).toBe(1);

      // Simulate database row with timestamps
      const dbRow = {
        ...row,
        created_at: sampleInquiry.createdAt.toISOString(),
        updated_at: sampleInquiry.updatedAt.toISOString()
      };

      const convertedInquiry = rowToInquiry(dbRow);
      expect(convertedInquiry.contactName).toBe(sampleInquiry.contactName);
      expect(convertedInquiry.contactEmail).toBe(sampleInquiry.contactEmail);
      expect(convertedInquiry.requiresProfessional).toBe(true);
    });

    it('should insert and retrieve inquiry', () => {
      const db = getDatabase();
      const row = inquiryToRow(sampleInquiry);
      
      const stmt = db.prepare(`
        INSERT INTO inquiries (
          id, contact_name, contact_email, company_name, 
          inquiry_category, inquiry_subcategory, urgency_level, 
          description, status, requires_professional
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        row.id, row.contact_name, row.contact_email, row.company_name,
        row.inquiry_category, row.inquiry_subcategory, row.urgency_level,
        row.description, row.status, row.requires_professional
      );

      const selectStmt = db.prepare('SELECT * FROM inquiries WHERE id = ?');
      const retrieved = selectStmt.get(sampleInquiry.id);
      
      expect(retrieved).toBeDefined();
      expect(retrieved.contact_name).toBe(sampleInquiry.contactName);
    });
  });

  describe('Resource Operations', () => {
    const sampleResource: Resource = {
      id: generateId(),
      title: 'Safety Checklist Template',
      description: 'Comprehensive workplace safety checklist',
      category: 'safety-checklists',
      type: 'template',
      fileUrl: '/resources/safety-checklist.pdf',
      previewUrl: '/resources/previews/safety-checklist.jpg',
      fileSize: 1024000,
      format: 'pdf',
      language: 'sq',
      downloadCount: 0,
      disclaimer: 'For educational purposes only',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('should convert resource to row and back', () => {
      const row = resourceToRow(sampleResource);
      expect(row.title).toBe(sampleResource.title);
      expect(row.file_url).toBe(sampleResource.fileUrl);
      expect(row.download_count).toBe(0);

      const dbRow = {
        ...row,
        created_at: sampleResource.createdAt.toISOString(),
        updated_at: sampleResource.updatedAt.toISOString()
      };

      const convertedResource = rowToResource(dbRow);
      expect(convertedResource.title).toBe(sampleResource.title);
      expect(convertedResource.fileUrl).toBe(sampleResource.fileUrl);
      expect(convertedResource.downloadCount).toBe(0);
    });

    it('should insert and retrieve resource', () => {
      const db = getDatabase();
      const row = resourceToRow(sampleResource);
      
      const stmt = db.prepare(`
        INSERT INTO resources (
          id, title, description, category, type, file_url, 
          preview_url, file_size, format, language, download_count, disclaimer
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        row.id, row.title, row.description, row.category, row.type, row.file_url,
        row.preview_url, row.file_size, row.format, row.language, row.download_count, row.disclaimer
      );

      const selectStmt = db.prepare('SELECT * FROM resources WHERE id = ?');
      const retrieved = selectStmt.get(sampleResource.id);
      
      expect(retrieved).toBeDefined();
      expect(retrieved.title).toBe(sampleResource.title);
    });
  });

  describe('User Activity Operations', () => {
    const sampleActivity: UserActivity = {
      id: generateId(),
      sessionId: 'session-123',
      userEmail: '<EMAIL>',
      ipAddress: '***********',
      activityType: 'resource_download',
      resourceId: 'resource-123',
      inquiryId: undefined,
      metadata: JSON.stringify({ browser: 'Chrome' }),
      timestamp: new Date()
    };

    it('should convert user activity to row and back', () => {
      const row = userActivityToRow(sampleActivity);
      expect(row.session_id).toBe(sampleActivity.sessionId);
      expect(row.activity_type).toBe(sampleActivity.activityType);
      expect(row.resource_id).toBe(sampleActivity.resourceId);

      const dbRow = {
        ...row,
        timestamp: sampleActivity.timestamp.toISOString()
      };

      const convertedActivity = rowToUserActivity(dbRow);
      expect(convertedActivity.sessionId).toBe(sampleActivity.sessionId);
      expect(convertedActivity.activityType).toBe(sampleActivity.activityType);
      expect(convertedActivity.resourceId).toBe(sampleActivity.resourceId);
    });

    it('should insert and retrieve user activity', () => {
      const db = getDatabase();
      
      // First create a resource to satisfy foreign key constraint
      const resourceId = generateId();
      const resourceStmt = db.prepare(`
        INSERT INTO resources (
          id, title, category, type, file_url, language, download_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      resourceStmt.run(resourceId, 'Test Resource', 'safety-checklists', 'template', '/test.pdf', 'sq', 0);
      
      // Create activity with valid resource reference
      const activityWithValidResource = {
        ...sampleActivity,
        resourceId: resourceId
      };
      
      const row = userActivityToRow(activityWithValidResource);
      
      const stmt = db.prepare(`
        INSERT INTO user_activities (
          id, session_id, user_email, ip_address, activity_type, 
          resource_id, inquiry_id, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        row.id, row.session_id, row.user_email, row.ip_address, row.activity_type,
        row.resource_id, row.inquiry_id, row.metadata
      );

      const selectStmt = db.prepare('SELECT * FROM user_activities WHERE id = ?');
      const retrieved = selectStmt.get(activityWithValidResource.id);
      
      expect(retrieved).toBeDefined();
      expect(retrieved.activity_type).toBe(activityWithValidResource.activityType);
      expect(retrieved.resource_id).toBe(resourceId);
    });

    it('should insert user activity without resource reference', () => {
      const db = getDatabase();
      
      // Create activity without resource reference (page view)
      const pageViewActivity: UserActivity = {
        id: generateId(),
        sessionId: 'session-456',
        userEmail: '<EMAIL>',
        ipAddress: '***********',
        activityType: 'page_view',
        resourceId: undefined,
        inquiryId: undefined,
        metadata: JSON.stringify({ page: '/about' }),
        timestamp: new Date()
      };
      
      const row = userActivityToRow(pageViewActivity);
      
      const stmt = db.prepare(`
        INSERT INTO user_activities (
          id, session_id, user_email, ip_address, activity_type, 
          resource_id, inquiry_id, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        row.id, row.session_id, row.user_email, row.ip_address, row.activity_type,
        row.resource_id, row.inquiry_id, row.metadata
      );

      const selectStmt = db.prepare('SELECT * FROM user_activities WHERE id = ?');
      const retrieved = selectStmt.get(pageViewActivity.id);
      
      expect(retrieved).toBeDefined();
      expect(retrieved.activity_type).toBe('page_view');
      expect(retrieved.resource_id).toBeNull();
    });
  });

  describe('Migration System', () => {
    it('should run migrations successfully', () => {
      expect(() => runMigrations()).not.toThrow();
    });

    it('should track migration status', () => {
      const status = getMigrationStatus();
      expect(Array.isArray(status)).toBe(true);
      expect(status.length).toBeGreaterThan(0);
      
      const firstMigration = status[0];
      expect(firstMigration).toHaveProperty('version');
      expect(firstMigration).toHaveProperty('name');
      expect(firstMigration).toHaveProperty('applied');
    });

    it('should handle rollbacks', () => {
      expect(() => rollbackMigrations(0)).not.toThrow();
    });
  });

  describe('Database Schema Validation', () => {
    it('should have all required tables', () => {
      const db = getDatabase();
      
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all() as { name: string }[];
      
      const tableNames = tables.map(t => t.name);
      
      expect(tableNames).toContain('inquiries');
      expect(tableNames).toContain('resources');
      expect(tableNames).toContain('user_activities');
      expect(tableNames).toContain('migrations');
    });

    it('should have proper indexes', () => {
      const db = getDatabase();
      
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all() as { name: string }[];
      
      const indexNames = indexes.map(i => i.name);
      
      expect(indexNames).toContain('idx_inquiries_status');
      expect(indexNames).toContain('idx_resources_category');
      expect(indexNames).toContain('idx_user_activities_type');
    });
  });
});