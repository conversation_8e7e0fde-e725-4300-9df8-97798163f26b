// This file will now act as a master list of case study IDs and their non-translatable data.
// All text content will be fetched from the JSON files using these IDs as keys.

interface CaseStudy {
  id: string;
  heroImage: string;
  client: string;
  location: string;
  duration: string;
  testimonialCompany: string;
}

const caseStudies: Record<string, CaseStudy> = {
  'trepca-safety-improvement': {
    id: 'trepca-safety-improvement',
    heroImage: '/images/case-study-trepca.jpg',
    client: 'Global Industry Research',
    location: 'International Study',
    duration: 'Multi-year Analysis',
    testimonialCompany: 'Safety Research Institute'
  },
  'highway-construction-safety': {
    id: 'highway-construction-safety',
    heroImage: '/images/case-study-highway.jpg',
    client: 'Global Training Research',
    location: 'International Study',
    duration: 'Multi-industry Analysis',
    testimonialCompany: 'Training Research Institute'
  },
  'qkuk-risk-assessment': {
    id: 'qkuk-risk-assessment',
    heroImage: '/images/case-study-qkuk.jpg',
    client: 'Global Compliance Research',
    location: 'International Study',
    duration: 'Multi-year Analysis',
    testimonialCompany: 'Compliance Research Institute'
  },
  // We can add other case studies here in the same manner
  'manufacturing-safety-overhaul': {
    id: 'manufacturing-safety-overhaul',
    heroImage: '/images/case-study-manufacturing.jpg',
    client: 'KBR',
    location: 'Ferizaj, Kosovo',
    duration: '12 muaj',
    testimonialCompany: 'KBR'
  },
};

// We also export an array of the IDs for easier mapping in components.
export const caseStudyIds = Object.keys(caseStudies);

export default caseStudies;