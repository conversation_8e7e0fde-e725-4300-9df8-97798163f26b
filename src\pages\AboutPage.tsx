import React, { useEffect } from 'react';

import { Award, CheckCircle, Users, Globe, Shield } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Hero from '../components/Hero';
import TeamMemberCard from '../components/TeamMemberCard';
import CtaSection from '../components/CtaSection';
import { getIconProps } from '../utils/iconUtils';
import { useSEO, generateStructuredData } from '../contexts/SEOContext';
// Images are now served from public folder
const milazimImage = '/images/Milazim.jpg';
const historyImage = '/images/history.jpg';

const AboutPage: React.FC = () => {
  const { t, i18n } = useTranslation('about');
  const currentLang = i18n.language;
  const { updateSEO } = useSEO();

  // Update SEO for about page
  useEffect(() => {
    const personSchema = generateStructuredData('Person', {
      name: '<PERSON><PERSON><PERSON><PERSON> Behluli',
      jobTitle: currentLang === 'sq' ? 'Themelues dhe Ekspert Sigurie' : 'Founder and Safety Expert',
      description: currentLang === 'sq'
        ? 'Ekspert i njohur në fushën e sigurisë në punë me përvojë të gjerë ndërkombëtare dhe themelues i SafeWork Kosova.'
        : 'Renowned workplace safety expert with extensive international experience and founder of SafeWork Kosova.',
      worksFor: {
        '@type': 'Organization',
        name: 'SafeWork Kosova'
      },
      knowsAbout: currentLang === 'sq'
        ? ['Siguria në Punë', 'Vlerësim Rreziku', 'Trajnime Sigurie', 'Konsulencë Sigurie']
        : ['Workplace Safety', 'Risk Assessment', 'Safety Training', 'Safety Consulting'],
      alumniOf: currentLang === 'sq' ? 'Universiteti i Prishtinës' : 'University of Pristina',
      nationality: 'Kosovo',
      image: '/images/Milazim.jpg'
    });



    updateSEO({
      title: currentLang === 'sq'
        ? 'Rreth Nesh - SafeWork Kosova | Milazim Behluli'
        : 'About Us - SafeWork Kosova | Milazim Behluli',
      description: currentLang === 'sq'
        ? 'Njihuni me SafeWork Kosova dhe themeluesin Milazim Behluli. Ekspert i njohur në siguri në punë me përvojë ndërkombëtare dhe pasion për ekselencën e sigurisë.'
        : 'Learn about SafeWork Kosova and founder Milazim Behluli. Renowned workplace safety expert with international experience and passion for safety excellence.',
      keywords: currentLang === 'sq'
        ? ['Milazim Behluli', 'SafeWork Kosova', 'ekspert sigurie', 'themelues', 'përvojë ndërkombëtare', 'siguria në punë']
        : ['Milazim Behluli', 'SafeWork Kosova', 'safety expert', 'founder', 'international experience', 'workplace safety'],
      type: 'website',
      structuredData: personSchema,
      openGraph: {
        title: currentLang === 'sq'
          ? 'Milazim Behluli - Ekspert Sigurie dhe Themelues i SafeWork Kosova'
          : 'Milazim Behluli - Safety Expert and Founder of SafeWork Kosova',
        description: currentLang === 'sq'
          ? 'Ekspert i njohur në siguri në punë me përvojë ndërkombëtare dhe themelues i SafeWork Kosova.'
          : 'Renowned workplace safety expert with international experience and founder of SafeWork Kosova.',
        type: 'profile'
      }
    });
  }, [currentLang, updateSEO]);

  const teamMembers = [
    {
      id: 1,
      name: t('team_member1_name'),
      position: t('team_member1_position'),
      image: milazimImage,
      bio: t('team_member1_bio'),
      certifications: [t('team_member1_cert1')]
    }
  ];

  // Use shared icon utilities for consistent styling
  const iconProps = getIconProps({ size: 'xl', color: 'secondary' });

  const values = [
    { icon: <Shield {...iconProps} />, title: t('value1_title'), description: t('value1_desc') },
    { icon: <CheckCircle {...iconProps} />, title: t('value2_title'), description: t('value2_desc') },
    { icon: <Users {...iconProps} />, title: t('value3_title'), description: t('value3_desc') },
    { icon: <Globe {...iconProps} />, title: t('value4_title'), description: t('value4_desc') }
  ];

  return (
    <div className="bg-white dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
      <Hero title={t('hero_title')} subtitle={t('hero_subtitle')} bgImage="/images/about-hero.jpg" />

      <section className="py-16 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2"><img src={historyImage} alt={t('history_title')} className="rounded-lg shadow-md w-full" /></div>
            <div className="md:w-1/2"><div className="mb-6"><h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('history_title')}</h2><p className="text-lg text-gray-600 dark:text-gray-300 mb-4">{t('history_p1')}</p><p className="text-lg text-gray-600 dark:text-gray-300 mb-4">{t('history_p2')}</p><p className="text-lg text-gray-600 dark:text-gray-300 mb-4">{t('history_p3')}</p></div></div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12"><h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('values_title')}</h2><p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('values_subtitle')}</p></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">{values.map((value, index) => (<div key={index} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-800"><div className="mb-4">{value.icon}</div><h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">{value.title}</h3><p className="text-gray-600 dark:text-gray-300">{value.description}</p></div>))}</div>
        </div>
      </section>

      {/* Certifications Section - Temporarily Hidden
      <section className="py-16 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2"><h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('certs_title')}</h2><p className="text-lg text-gray-600 dark:text-gray-300 mb-6">{t('certs_subtitle')}</p>
              <div className="space-y-4">
                <div className="flex items-start"><Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" /><div><h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('cert1_title')}</h3><p className="text-gray-600 dark:text-gray-300">{t('cert1_desc')}</p></div></div>
                <div className="flex items-start"><Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" /><div><h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('cert2_title')}</h3><p className="text-gray-600 dark:text-gray-300">{t('cert2_desc')}</p></div></div>
                <div className="flex items-start"><Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" /><div><h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('cert3_title')}</h3><p className="text-gray-600 dark:text-gray-300">{t('cert3_desc')}</p></div></div>
                <div className="flex items-start"><Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" /><div><h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('cert4_title')}</h3><p className="text-gray-600 dark:text-gray-300">{t('cert4_desc')}</p></div></div>
              </div>
            </div>
            <div className="md:w-1/2"><div className="grid grid-cols-2 gap-6"><div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-800 flex items-center justify-center"><div className="text-center"><Award className="w-16 h-16 text-blue-800 mx-auto mb-2" /><p className="font-bold text-gray-900 dark:text-gray-100">ISO 45001</p></div></div><div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-800 flex items-center justify-center"><div className="text-center"><Award className="w-16 h-16 text-blue-800 mx-auto mb-2" /><p className="font-bold text-gray-900 dark:text-gray-100">OSHA VPP</p></div></div><div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-800 flex items-center justify-center"><div className="text-center"><Award className="w-16 h-16 text-blue-800 mx-auto mb-2" /><p className="font-bold text-gray-900 dark:text-gray-100">NSC</p></div></div><div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-800 flex items-center justify-center"><div className="text-center"><Award className="w-16 h-16 text-blue-800 mx-auto mb-2" /><p className="font-bold text-gray-900 dark:text-gray-100">ASSP</p></div></div></div></div>
          </div>
        </div>
      </section>
      */}

      {/* Meet the Founder Section - Temporarily Hidden
      <section id="team" className="py-16 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12"><h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('team_title')}</h2><p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('team_subtitle')}</p></div>
          <div className="grid grid-cols-1 gap-8 max-w-2xl mx-auto">{teamMembers.map((member) => (<TeamMemberCard key={member.id} name={member.name} position={member.position} image={member.image} bio={member.bio} certifications={member.certifications} />))}</div>

        </div>
      </section>
      */}

      <CtaSection 
        title={t('cta_title')} 
        description={t('cta_subtitle')} 
        ctaText={t('cta_button')} 
        ctaLink={`/${currentLang}/contact#formular-kontakti`} 
      />
    </div>
  );
};

export default AboutPage;