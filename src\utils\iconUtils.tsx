
// Icon Size Configurations
export const ICON_SIZES = {
    xs: 'w-4 h-4',
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-10 h-10',
    '2xl': 'w-12 h-12',
    '3xl': 'w-16 h-16'
} as const;

export type IconSize = keyof typeof ICON_SIZES;

// Icon Color Configurations
export const ICON_COLORS = {
    primary: 'rgb(var(--color-primary-500))',
    secondary: 'rgb(var(--color-secondary-500))',
    accent: 'rgb(var(--color-accent))',
    success: 'rgb(var(--color-success))',
    warning: 'rgb(var(--color-warning))',
    error: 'rgb(var(--color-error))',
    muted: 'rgb(var(--color-text-muted))',
    current: 'currentColor'
} as const;

export type IconColor = keyof typeof ICON_COLORS;

// Icon Styling Utility
export interface IconStyleProps {
    size?: IconSize;
    color?: IconColor;
    className?: string;
}

export const getIconProps = ({
    size = 'md',
    color = 'secondary',
    className = ''
}: IconStyleProps = {}) => {
    const sizeClass = ICON_SIZES[size];
    const colorStyle = ICON_COLORS[color];

    return {
        className: `${sizeClass} ${className}`.trim(),
        style: color !== 'current' ? { color: colorStyle } : undefined
    };
};

