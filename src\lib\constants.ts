// Centralized constants and configuration
import type { LanguageConfig, NavigationItem } from '../types';

// Application Configuration
export const APP_CONFIG = {
  name: 'SafeWork Kosova',
  description: 'Professional workplace safety services in Kosovo',
  version: '1.0.0',
  author: '<PERSON><PERSON><PERSON><PERSON>',
  url: 'https://www.safework-kosova.com',
  email: '<EMAIL>',
  phone: '+383-44-819-701',
} as const;

// Supported Languages
export const LANGUAGES: LanguageConfig[] = [
  {
    code: 'sq',
    name: 'Albanian',
    nativeName: 'Shqip',
    flag: '🇽🇰',
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
];

// Default Language
export const DEFAULT_LANGUAGE = 'sq';

// API Configuration
export const API_CONFIG = {
  baseUrl: '/api',
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  defaultMode: 'system',
  storageKey: 'safework-theme',
  transitionDuration: 300,
} as const;

// SEO Configuration
export const SEO_CONFIG = {
  defaultTitle: 'SafeWork Kosova - Workplace Safety Excellence',
  titleTemplate: '%s | SafeWork Kosova',
  defaultDescription: 'Professional workplace safety services, risk assessments, training and consulting for businesses in Kosovo.',
  defaultImage: '/images/safework-kosova-og.png',
  twitterHandle: '@safeworkkosova',
} as const;

// Navigation Configuration
export const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    id: 'home',
    label: 'nav_home',
    href: '/',
  },
  {
    id: 'about',
    label: 'nav_about',
    href: '/about',
  },
  {
    id: 'services',
    label: 'nav_services',
    href: '/services',
  },
  {
    id: 'case-studies',
    label: 'nav_case_studies',
    href: '/case-studies',
  },
  {
    id: 'blog',
    label: 'nav_blog',
    href: '/blog',
  },
  {
    id: 'contact',
    label: 'nav_contact',
    href: '/contact',
  },
];

// Form Configuration
export const FORM_CONFIG = {
  contact: {
    maxMessageLength: 1000,
    requiredFields: ['name', 'email', 'message'],
    autoSaveDraft: true,
  },
  newsletter: {
    confirmationRequired: false,
    doubleOptIn: false,
  },
} as const;

// Pagination Configuration
export const PAGINATION_CONFIG = {
  defaultPageSize: 10,
  maxPageSize: 50,
  showPageNumbers: 5,
} as const;

// Animation Configuration
export const ANIMATION_CONFIG = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  easing: {
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Social Media Links
export const SOCIAL_LINKS = {
  facebook: 'https://facebook.com/safeworkkosova',
  linkedin: 'https://linkedin.com/company/safework-kosova',
  twitter: 'https://twitter.com/safeworkkosova',
  instagram: 'https://instagram.com/safeworkkosova',
} as const;

// Contact Information
export const CONTACT_INFO = {
  phone: '+383-44-819-701',
  email: '<EMAIL>',
  address: {
    street: 'Fatmir Ratkoceri Street',
    city: 'Ferizaj',
    postalCode: '70000',
    country: 'Kosovo',
  },
  hours: {
    weekdays: '09:00 - 17:00',
    weekend: 'Closed',
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  network: 'Network error. Please check your connection.',
  server: 'Server error. Please try again later.',
  validation: 'Please check your input and try again.',
  notFound: 'The requested resource was not found.',
  unauthorized: 'You are not authorized to perform this action.',
  rateLimit: 'Too many requests. Please try again later.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  contactSubmitted: 'Your message has been sent successfully!',
  newsletterSubscribed: 'Thank you for subscribing to our newsletter!',
  formSaved: 'Your progress has been saved.',
} as const;
