import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import ScrollToTop from '../components/ScrollToTop';
import SEOHead from '../components/SEOHead';

const MainLayout: React.FC = () => {
  const [showScrollToTop, setShowScrollToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setShowScrollToTop(true);
      } else {
        setShowScrollToTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <SEOHead />
      <Header />
      <main id="main-content" className="flex-grow" role="main" tabIndex={-1}>
        <Outlet />
      </main>
      <Footer />
      <CookieConsent />
      {showScrollToTop && <ScrollToTop />}
    </div>
  );
};

export default MainLayout;