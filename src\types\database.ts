// Core database interfaces for the CRM system

export interface Inquiry {
  id: string;
  contactName: string;
  contactEmail: string;
  companyName?: string;
  inquiryCategory: InquiryCategory;
  inquirySubcategory?: string;
  urgencyLevel: UrgencyLevel;
  description: string;
  status: InquiryStatus;
  assignedTo?: string;
  requiresProfessional: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Resource {
  id: string;
  title: string;
  description?: string;
  category: ResourceCategory;
  type: ResourceType;
  fileUrl: string;
  previewUrl?: string;
  fileSize?: number;
  format?: string;
  language: 'sq' | 'en';
  downloadCount: number;
  disclaimer?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserActivity {
  id: string;
  sessionId?: string;
  userEmail?: string;
  ipAddress?: string;
  activityType: ActivityType;
  resourceId?: string;
  inquiryId?: string;
  metadata?: string; // JSON string
  timestamp: Date;
}

// Enums and types
export type InquiryCategory = 
  | 'risk-assessment' 
  | 'training' 
  | 'compliance' 
  | 'emergency' 
  | 'general';

export type UrgencyLevel = 'low' | 'medium' | 'high' | 'emergency';

export type InquiryStatus = 
  | 'new' 
  | 'in-progress' 
  | 'responded' 
  | 'closed' 
  | 'referred';

export type ResourceCategory = 
  | 'risk-assessment' 
  | 'training-materials' 
  | 'compliance-templates' 
  | 'safety-checklists' 
  | 'emergency-procedures' 
  | 'jsa-templates';

export type ResourceType = 
  | 'template' 
  | 'guide' 
  | 'checklist' 
  | 'video' 
  | 'document' 
  | 'form';

export type ActivityType = 
  | 'resource_download' 
  | 'inquiry_submit' 
  | 'page_view' 
  | 'search';

// Database row interfaces (for SQLite)
export interface InquiryRow {
  id: string;
  contact_name: string;
  contact_email: string;
  company_name?: string;
  inquiry_category: string;
  inquiry_subcategory?: string;
  urgency_level: string;
  description: string;
  status: string;
  assigned_to?: string;
  requires_professional: number; // SQLite boolean as integer
  created_at: string; // SQLite datetime as string
  updated_at: string;
}

export interface ResourceRow {
  id: string;
  title: string;
  description?: string;
  category: string;
  type: string;
  file_url: string;
  preview_url?: string;
  file_size?: number;
  format?: string;
  language: string;
  download_count: number;
  disclaimer?: string;
  created_at: string;
  updated_at: string;
}

export interface UserActivityRow {
  id: string;
  session_id?: string;
  user_email?: string;
  ip_address?: string;
  activity_type: string;
  resource_id?: string;
  inquiry_id?: string;
  metadata?: string;
  timestamp: string;
}