import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import sitemapPlugin from 'vite-plugin-sitemap';

// Define routes for sitemap generation
const routes = [
  // Albanian routes
  '/sq',
  '/sq/about',
  '/sq/services',
  '/sq/case-studies',
  '/sq/blog',
  '/sq/contact',
  '/sq/privacy',
  '/sq/terms',
  // English routes
  '/en',
  '/en/about',
  '/en/services',
  '/en/case-studies',
  '/en/blog',
  '/en/contact',
  '/en/privacy',
  '/en/terms',
];

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    sitemapPlugin({
      hostname: 'https://www.safework-kosova.com', // Replace with actual domain
      dynamicRoutes: routes,
      changefreq: 'weekly',
      priority: 0.8,
      lastmod: new Date(),
    }),
  ],

  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
  build: {
    // Performance optimization
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          i18n: ['i18next', 'react-i18next', 'i18next-browser-languagedetector', 'i18next-http-backend'],
          ui: ['lucide-react'],
        },
      },
    },
    // Enable source maps for production debugging
    sourcemap: true,
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: ['react-helmet-async', 'web-vitals'],
  },
});
