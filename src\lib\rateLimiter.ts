// Rate limiting storage (in-memory for simplicity)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Check if request is within rate limit
 */
export function checkRateLimit(
  key: string, 
  limit: number = 5, 
  windowMs: number = 60 * 60 * 1000 // 1 hour default
): boolean {
  const now = Date.now();

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const record = rateLimitStore.get(key)!;

  if (now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

/**
 * Get remaining requests for a key
 */
export function getRemainingRequests(
  key: string, 
  limit: number = 5
): { remaining: number; resetTime: number } {
  const now = Date.now();
  
  if (!rateLimitStore.has(key)) {
    return { remaining: limit, resetTime: now + 60 * 60 * 1000 };
  }

  const record = rateLimitStore.get(key)!;

  if (now > record.resetTime) {
    return { remaining: limit, resetTime: now + 60 * 60 * 1000 };
  }

  return { 
    remaining: Math.max(0, limit - record.count), 
    resetTime: record.resetTime 
  };
}

/**
 * Clear expired entries from rate limit store
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now();
  
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Clean up expired entries every 10 minutes
setInterval(cleanupRateLimitStore, 10 * 60 * 1000);