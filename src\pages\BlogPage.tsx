import React, { useState, useEffect, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight, Calendar, User, Tag, Clock, Search } from 'lucide-react';
import Hero from '../components/Hero';
// Image is now served from public folder
const blogImage = '/images/blog.jpg';
import CtaSection from '../components/CtaSection';
import { blogPostsData } from '../data/blogPosts';
import { validateNewsletterEmail } from '../lib/validators';
import { suggestEmailCorrection } from '../utils/securityUtils';
import { useSEO, generateStructuredData } from '../contexts/SEOContext';

interface BlogPostDisplay {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  author: string;
  authorTitle: string;
  category: string;
  readTime: string;
  content: string;
}

const BlogPage: React.FC = () => {
  const { t, i18n } = useTranslation('blog');
  const currentLang = i18n.language;
  const location = useLocation();
  const navigate = useNavigate();
  const { updateSEO } = useSEO();

  const allPosts: BlogPostDisplay[] = useMemo(() =>
    blogPostsData.map(post => ({
      ...post,
      author: t(post.author), // Translate author name
      authorTitle: t(post.authorTitleKey), // Translate author title
      readTime: `${post.readTimeMinutes} ${t('read_time_suffix')}`, // Construct read time
      title: t(`${post.id}.title`),
      excerpt: t(`${post.id}.excerpt`),
      category: t(`${post.id}.category`),
      content: t(`${post.id}.content`), // Needed for searching
    })),
    [t]);

  const categories = useMemo(() => [
    t('category_all'),
    ...[...new Set(allPosts.map(p => p.category))]
  ], [t, allPosts]);

  const [activeCategory, setActiveCategory] = useState(categories[0]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 4;

  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage('');

    // Client-side validation using new validation system
    const validation = validateNewsletterEmail(email);
    if (!validation.isValid) {
      const suggestion = suggestEmailCorrection(email.trim());
      if (suggestion) {
        setMessage(t('sidebar_subscribe_error_typo', { suggestion }));
      } else {
        setMessage(validation.error || t('sidebar_subscribe_error_invalid'));
      }
      return;
    }

    setIsSubscribing(true);

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim(), language: currentLang }),
      });

      // Log the raw response for debugging
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      // Get response text first to see what we're actually getting
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        console.error('Response was:', responseText);
        throw new Error('Server returned invalid response');
      }

      if (response.ok) {
        setMessage(t('sidebar_subscribe_success'));
        setEmail('');
      } else {
        console.error('Subscription failed:', response.status, data);
        setMessage(data.error || data.message || t('sidebar_subscribe_error_invalid'));
      }
    } catch (error) {
      console.error('Network error during subscription:', error);
      setMessage(t('sidebar_subscribe_error_general'));
    } finally {
      setIsSubscribing(false);
    }
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const category = params.get('category');
    if (category && categories.includes(category)) {
      setActiveCategory(category);
    }
  }, [location.search, categories]);

  // Reset activeCategory when language changes to ensure it matches the new translation
  useEffect(() => {
    setActiveCategory(t('category_all'));
  }, [currentLang, t]);

  // Update SEO for blog page
  useEffect(() => {
    const blogSchema = generateStructuredData('Blog', {
      name: currentLang === 'sq' ? 'Blog - SafeWork Kosova' : 'Blog - SafeWork Kosova',
      description: currentLang === 'sq'
        ? 'Artikuj dhe këshilla profesionale për siguri në punë, vlerësim rreziku dhe shëndetësi profesionale nga ekspertët e SafeWork Kosova.'
        : 'Professional articles and tips on workplace safety, risk assessment and occupational health from SafeWork Kosova experts.',
      publisher: {
        '@type': 'Organization',
        name: 'SafeWork Kosova',
        logo: '/images/safework-kosova-logo.png'
      },
      author: {
        '@type': 'Person',
        name: 'Milazim Behluli',
        jobTitle: currentLang === 'sq' ? 'Ekspert Sigurie' : 'Safety Expert'
      },
      inLanguage: currentLang === 'sq' ? 'sq' : 'en',
      about: currentLang === 'sq'
        ? ['Siguria në Punë', 'Vlerësim Rreziku', 'Trajnime Sigurie', 'Shëndetësia Profesionale']
        : ['Workplace Safety', 'Risk Assessment', 'Safety Training', 'Occupational Health']
    });

    updateSEO({
      title: currentLang === 'sq'
        ? 'Blog - SafeWork Kosova | Artikuj dhe Këshilla Sigurie'
        : 'Blog - SafeWork Kosova | Safety Articles and Tips',
      description: currentLang === 'sq'
        ? 'Lexoni artikuj profesionalë për siguri në punë, vlerësim rreziku, trajnime dhe shëndetësi profesionale. Këshilla eksperte nga SafeWork Kosova.'
        : 'Read professional articles on workplace safety, risk assessment, training and occupational health. Expert advice from SafeWork Kosova.',
      keywords: currentLang === 'sq'
        ? ['blog sigurie', 'artikuj sigurie', 'këshilla sigurie', 'siguria në punë', 'vlerësim rreziku', 'trajnime sigurie']
        : ['safety blog', 'safety articles', 'safety tips', 'workplace safety', 'risk assessment', 'safety training'],
      type: 'blog',
      structuredData: blogSchema,
      openGraph: {
        title: currentLang === 'sq'
          ? 'Blog SafeWork Kosova - Artikuj dhe Këshilla Sigurie'
          : 'SafeWork Kosova Blog - Safety Articles and Tips',
        description: currentLang === 'sq'
          ? 'Artikuj profesionalë dhe këshilla eksperte për siguri në punë nga SafeWork Kosova.'
          : 'Professional articles and expert advice on workplace safety from SafeWork Kosova.',
        type: 'website'
      }
    });
  }, [currentLang, updateSEO]);

  const filteredPosts = useMemo(() => {
    return allPosts.filter(post => {
      const matchesCategory = activeCategory === t('category_all') || post.category === activeCategory;
      const matchesSearch = searchQuery.trim() === '' ||
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }, [allPosts, activeCategory, searchQuery, t]);

  const handleCategoryClick = (category: string) => {
    setActiveCategory(category);
    setCurrentPage(1);
    navigate(`/${currentLang}/blog`);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);

  return (
    <div>
      <Hero title={t('page_title')} subtitle={t('page_subtitle')} ctaText={t('page_cta')} ctaLink="#subscribe" bgImage={blogImage} />
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col lg:flex-row gap-12">
            <div className="lg:w-2/3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {currentPosts.length > 0 ? (
                  currentPosts.map((post) => (
                    <article key={post.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-gray-700">
                      <div className="relative h-48 overflow-hidden">
                        <Link to={`/${currentLang}/blog/${post.id}`}>
                          <img src={post.image} alt={post.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-105" />
                        </Link>
                        <div className="absolute top-4 left-4">
                          <span className="inline-block bg-blue-800 text-white text-sm font-medium py-1 px-3 rounded-full dark:bg-blue-900 dark:text-blue-200">{post.category}</span>
                        </div>
                      </div>
                      <div className="p-6">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                          <User className="w-4 h-4 mr-1" />
                          <span className="mr-4">{post.author}</span>
                          <Calendar className="w-4 h-4 mr-1" />
                          <span className="mr-4">{post.date}</span>
                          <Clock className="w-4 h-4 mr-1" />
                          <span>{post.readTime}</span>
                        </div>
                        <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                          <Link to={`/${currentLang}/blog/${post.id}`} className="hover:text-blue-800 dark:hover:text-blue-300 transition-colors">{post.title}</Link>
                        </h2>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">{post.excerpt}</p>
                        <Link to={`/${currentLang}/blog/${post.id}`} className="inline-flex items-center text-blue-800 dark:text-blue-400 font-medium hover:text-orange-500 dark:hover:text-orange-400 transition-colors">{t('common:button_readMore')}<ArrowRight className="ml-2 w-4 h-4" /></Link>
                      </div>
                    </article>
                  ))
                ) : (
                  <div className="col-span-2 py-12 text-center">
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">{t('no_posts_found_title')}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">{t('no_posts_found_desc')}</p>
                    <button onClick={() => { setActiveCategory(t('category_all')); setSearchQuery(''); }} className="inline-flex items-center text-blue-800 dark:text-blue-400 font-medium hover:text-orange-500 dark:hover:text-orange-400 transition-colors">{t('no_posts_found_button')}<ArrowRight className="ml-2 w-4 h-4" /></button>
                  </div>
                )}
              </div>
              <div className="mt-12 flex justify-center">
                <nav className="inline-flex rounded-md shadow">
                  <button className="px-3 py-2 rounded-l-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" onClick={() => setCurrentPage(p => p - 1)} disabled={currentPage === 1}>{t('pagination_prev')}</button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNumber => (
                    <button
                      key={pageNumber}
                      className={`px-3 py-2 border-t border-b border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium ${pageNumber === currentPage ? 'text-blue-800 dark:text-blue-300' : 'text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                      onClick={() => setCurrentPage(pageNumber)}
                    >
                      {pageNumber}
                    </button>
                  ))}
                  <button className="px-3 py-2 rounded-r-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" onClick={() => setCurrentPage(p => p + 1)} disabled={currentPage === totalPages}>{t('pagination_next')}</button>
                </nav>
              </div>
            </div>
            <div className="lg:w-1/3">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-6 mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">{t('search_title')}</h3>
                <form onSubmit={handleSearchSubmit} className="relative">
                  <input type="text" placeholder={t('search_placeholder')} className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
                  <button type="submit" className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"><Search className="w-5 h-5" /></button>
                </form>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-6 mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">{t('categories_title')}</h3>
                <ul className="space-y-2">
                  {categories.map((category) => (
                    <li key={category}>
                      <button
                        className={`flex items-center text-left w-full ${activeCategory === category
                          ? 'text-blue-800 dark:text-blue-300 font-medium'
                          : 'text-gray-700 dark:text-gray-200 hover:text-blue-800 dark:hover:text-blue-300'}`}
                        onClick={() => handleCategoryClick(category)}
                      >
                        <Tag className="w-4 h-4 mr-2 flex-shrink-0" />
                        {category}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
              <div id="subscribe" className="sticky top-24 bg-blue-800 dark:bg-gray-800 rounded-lg p-6 text-white dark:text-gray-100">
                <h3 className="text-lg font-bold mb-4">{t('sidebar_subscribe_title')}</h3>
                <p className="text-blue-100 dark:text-gray-300 mb-6">{t('sidebar_subscribe_desc')}</p>
                <form onSubmit={handleSubscribe} className="space-y-4">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('sidebar_subscribe_placeholder')}
                    className="w-full px-4 py-2 rounded-md text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-900 focus:ring-2 focus:ring-orange-500 focus:outline-none border border-transparent dark:border-gray-700"
                    required
                  />
                  <button
                    type="submit"
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-md transition-colors dark:bg-orange-600 dark:hover:bg-orange-500 dark:text-white"
                    disabled={isSubscribing}
                  >
                    {isSubscribing ? 'Subscribing...' : t('sidebar_subscribe_button')}
                  </button>
                  {message && <p className="text-sm text-center">{message}</p>}
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
      <CtaSection title={t('cta_title')} description={t('cta_subtitle')} ctaText={t('cta_button')} ctaLink={`/${currentLang}/contact#formular-kontakti`} />
    </div>
  );
};

export default BlogPage;
