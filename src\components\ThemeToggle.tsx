import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../utils/themeUtils';
import { useTranslation } from 'react-i18next';

// Theme Toggle Props
export interface ThemeToggleProps {
  variant?: 'button' | 'switch' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

// Icon components for theme states
const SunIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
    />
  </svg>
);

const MoonIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
    />
  </svg>
);

const SystemIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
    />
  </svg>
);

const ChevronDownIcon: React.FC<{ className?: string }> = ({ className = "w-4 h-4" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

// Button variant component
const ButtonVariant: React.FC<ThemeToggleProps> = ({ size = 'md', showLabel = false, className = '' }) => {
  const { theme, effectiveTheme, toggleTheme } = useTheme();
  const { t } = useTranslation();

  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-2.5',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const getCurrentIcon = () => {
    if (theme === 'system') {
      return <SystemIcon className={iconSizeClasses[size]} />;
    }
    return effectiveTheme === 'dark' ? 
      <MoonIcon className={iconSizeClasses[size]} /> : 
      <SunIcon className={iconSizeClasses[size]} />;
  };

  const getAriaLabel = () => {
    if (theme === 'system') {
      return t('theme.toggleToManual', 'Switch to manual theme control');
    }
    return effectiveTheme === 'dark' ? 
      t('theme.switchToLight', 'Switch to light theme') : 
      t('theme.switchToDark', 'Switch to dark theme');
  };

  return (
    <button
      onClick={toggleTheme}
      className={`
        inline-flex items-center justify-center
        ${sizeClasses[size]}
        rounded-md
        transition-all duration-theme
        focus:outline-none focus:ring-2 focus:ring-offset-2
        hover:scale-105
        ${className}
      `}
      style={{
        backgroundColor: 'rgb(var(--color-surface))',
        borderColor: 'rgb(var(--color-border))',
        border: '1px solid',
        color: 'rgb(var(--color-primary-500))',
        '--tw-ring-color': 'rgb(var(--color-primary-500))'
      } as React.CSSProperties}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = 'rgb(var(--color-surface-hover))';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'rgb(var(--color-surface))';
      }}
      aria-label={getAriaLabel()}
      title={getAriaLabel()}
    >
      <span className="transition-transform duration-theme hover:rotate-12">
        {getCurrentIcon()}
      </span>
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {theme === 'system' ? t('theme.system', 'System') : 
           effectiveTheme === 'dark' ? t('theme.dark', 'Dark') : t('theme.light', 'Light')}
        </span>
      )}
    </button>
  );
};

// Switch variant component
const SwitchVariant: React.FC<ThemeToggleProps> = ({ size = 'md', showLabel = false, className = '' }) => {
  const { theme, effectiveTheme, setTheme } = useTheme();
  const { t } = useTranslation();

  const sizeClasses = {
    sm: 'w-10 h-5',
    md: 'w-12 h-6',
    lg: 'w-14 h-7',
  };

  const thumbSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const handleToggle = () => {
    if (theme === 'system') {
      setTheme(effectiveTheme === 'dark' ? 'light' : 'dark');
    } else {
      setTheme(theme === 'dark' ? 'light' : 'dark');
    }
  };

  const isChecked = effectiveTheme === 'dark';

  return (
    <div className={`flex items-center ${className}`}>
      {showLabel && (
        <span className="mr-3 text-sm font-medium text-secondary">
          <SunIcon className="w-4 h-4 inline mr-1" />
          {t('theme.light', 'Light')}
        </span>
      )}
      
      <button
        role="switch"
        aria-checked={isChecked}
        onClick={handleToggle}
        className={`
          relative inline-flex
          ${sizeClasses[size]}
          bg-surface-variant
          border border-border
          rounded-full
          transition-all duration-theme
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
          ${isChecked ? 'bg-primary-500' : 'bg-surface-variant'}
        `}
        aria-label={t('theme.toggle', 'Toggle theme')}
      >
        <span
          className={`
            inline-block
            ${thumbSizeClasses[size]}
            bg-white
            rounded-full
            shadow-sm
            transition-transform duration-theme
            ${isChecked ? 'translate-x-6' : 'translate-x-0.5'}
            ${size === 'sm' && isChecked ? 'translate-x-5' : ''}
            ${size === 'lg' && isChecked ? 'translate-x-7' : ''}
          `}
        >
          <span className="flex items-center justify-center w-full h-full text-gray-600">
            {isChecked ? 
              <MoonIcon className="w-3 h-3" /> : 
              <SunIcon className="w-3 h-3" />
            }
          </span>
        </span>
      </button>

      {showLabel && (
        <span className="ml-3 text-sm font-medium text-secondary">
          <MoonIcon className="w-4 h-4 inline mr-1" />
          {t('theme.dark', 'Dark')}
        </span>
      )}
    </div>
  );
};

// Dropdown variant component
const DropdownVariant: React.FC<ThemeToggleProps> = ({ size = 'md', showLabel = false, className = '' }) => {
  const { theme, effectiveTheme, setTheme } = useTheme();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2.5 text-base',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen]);

  const getCurrentIcon = () => {
    if (theme === 'system') {
      return <SystemIcon className={iconSizeClasses[size]} />;
    }
    return effectiveTheme === 'dark' ? 
      <MoonIcon className={iconSizeClasses[size]} /> : 
      <SunIcon className={iconSizeClasses[size]} />;
  };

  const getCurrentLabel = () => {
    if (theme === 'system') {
      return t('theme.system', 'System');
    }
    return effectiveTheme === 'dark' ? t('theme.dark', 'Dark') : t('theme.light', 'Light');
  };

  const options = [
    { value: 'light', label: t('theme.light', 'Light'), icon: SunIcon },
    { value: 'dark', label: t('theme.dark', 'Dark'), icon: MoonIcon },
    { value: 'system', label: t('theme.system', 'System'), icon: SystemIcon },
  ];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          inline-flex items-center justify-between
          ${sizeClasses[size]}
          bg-surface hover:bg-surface-variant
          border border-border
          rounded-md
          text-primary
          transition-all duration-theme
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
          min-w-[120px]
        `}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label={t('theme.selectTheme', 'Select theme')}
      >
        <div className="flex items-center">
          {getCurrentIcon()}
          {showLabel && (
            <span className="ml-2 font-medium">
              {getCurrentLabel()}
            </span>
          )}
        </div>
        <ChevronDownIcon 
          className={`w-4 h-4 ml-2 transition-transform duration-theme ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-full min-w-[140px] bg-surface border border-border rounded-md shadow-lg z-50">
          <div className="py-1" role="listbox">
            {options.map((option) => {
              const Icon = option.icon;
              const isSelected = theme === option.value;
              
              return (
                <button
                  key={option.value}
                  onClick={() => {
                    setTheme(option.value as ThemeMode);
                    setIsOpen(false);
                  }}
                  className={`
                    w-full flex items-center px-3 py-2 text-sm
                    hover:bg-surface-variant
                    transition-colors duration-theme
                    ${isSelected ? 'bg-primary-50 text-primary-600 dark:bg-primary-900 dark:text-primary-300' : 'text-secondary'}
                  `}
                  role="option"
                  aria-selected={isSelected}
                >
                  <Icon className="w-4 h-4 mr-3" />
                  <span className="font-medium">{option.label}</span>
                  {isSelected && (
                    <svg
                      className="w-4 h-4 ml-auto"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

// Main ThemeToggle component
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  variant = 'button', 
  ...props 
}) => {
  switch (variant) {
    case 'switch':
      return <SwitchVariant variant={variant} {...props} />;
    case 'dropdown':
      return <DropdownVariant variant={variant} {...props} />;
    case 'button':
    default:
      return <ButtonVariant variant={variant} {...props} />;
  }
};

export default ThemeToggle;