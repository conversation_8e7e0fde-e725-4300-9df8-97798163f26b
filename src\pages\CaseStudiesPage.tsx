import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';
// Image is now served from public folder
const ComingSoonImage = '/images/coming_soon.jpg';

const CaseStudiesPage: React.FC = () => {
  const { t, i18n } = useTranslation('case-studies');
  const currentLang = i18n.language;


  return (
    <div>
      <Hero title={t('page_title')} subtitle={t('page_subtitle')} ctaText={t('page_cta')} ctaLink={`/${currentLang}/contact#formular-kontakti`} bgImage={ComingSoonImage} />
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          {/* Coming Soon Content */}
          <div className="text-center py-16">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                {t('coming_soon_title')}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-12 leading-relaxed">
                {t('coming_soon_description')}
              </p>
              
              {/* Industries We Serve */}
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 008 9.172V5L8 4z" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_manufacturing')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_manufacturing_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_construction')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_construction_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_healthcare')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_healthcare_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_oilgas')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_oilgas_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-1M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_transport')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_transport_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_warehousing')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_warehousing_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.701 2.701 0 00-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zM3 9h18v10a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_foodbev')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_foodbev_desc')}
                  </p>
                </article>
                
                <article 
                  className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                  style={{
                    backgroundColor: 'rgb(var(--color-surface))',
                    borderColor: 'rgb(var(--color-border))'
                  }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                    style={{ 
                      backgroundColor: 'rgb(var(--color-secondary-50))',
                      color: 'rgb(var(--color-secondary-500))'
                    }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 
                    className="text-lg font-semibold mb-3 text-center"
                    style={{ color: 'rgb(var(--color-text-primary))' }}
                  >
                    {t('industry_utilities')}
                  </h3>
                  <p 
                    className="text-sm text-center"
                    style={{ color: 'rgb(var(--color-text-secondary))' }}
                  >
                    {t('industry_utilities_desc')}
                  </p>
                </article>
              </div>

              {/* Call to Action */}
              <div className="bg-gradient-to-r from-blue-50 to-orange-50 dark:from-gray-800 dark:to-gray-700 p-8 rounded-lg border border-blue-100 dark:border-gray-600">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                  {t('coming_soon_cta_title')}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {t('coming_soon_cta_description')}
                </p>
                <Link 
                  to={`/${currentLang}/contact#formular-kontakti`} 
                  className="btn-primary-lg inline-flex items-center"
                >
                  {t('coming_soon_cta_button')}
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <CtaSection title={t('cta_title')} description={t('cta_subtitle')} ctaText={t('cta_button')} ctaLink={`/${currentLang}/contact#formular-kontakti`} />
    </div>
  );
};

export default CaseStudiesPage;