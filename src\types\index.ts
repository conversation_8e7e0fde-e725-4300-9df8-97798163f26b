// Global type definitions for SafeWork Kosova

// API Response Types
export interface ApiResponse<T = unknown> {
  data?: T;
  success: boolean;
  message?: string;
  error?: string;
  details?: string[];
}

// Contact Form Types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
  service?: string;
}

export interface ContactFormErrors {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  message?: string;
  service?: string;
}

// Newsletter Types
export interface NewsletterSubscription {
  email: string;
}

// Blog Types
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  category: string;
  tags: string[];
  readTime: number;
  image?: string;
  slug: string;
}

export interface BlogCategory {
  id: string;
  name: string;
  count: number;
}

// Service Types
export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  benefits: string[];
  image?: string;
  slug: string;
}

export interface ServiceDetail extends Service {
  longDescription: string;
  process: ServiceProcess[];
  pricing?: ServicePricing;
  faqs: FAQ[];
}

export interface ServiceProcess {
  step: number;
  title: string;
  description: string;
  duration?: string;
}

export interface ServicePricing {
  type: 'fixed' | 'hourly' | 'project';
  price?: number;
  currency: string;
  description: string;
}

// Case Study Types
export interface CaseStudy {
  id: string;
  title: string;
  client: string;
  industry: string;
  challenge: string;
  solution: string;
  results: string[];
  image?: string;
  date: string;
  slug: string;
}

// Team Types
export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image?: string;
  certifications: string[];
  experience: number;
  specialties: string[];
}

// Testimonial Types
export interface Testimonial {
  id: number;
  quote: string;
  author: string;
  position: string;
  company: string;
  image: string;
  rating?: number;
}

// FAQ Types
export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

// SEO Types
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  image?: string;
  url?: string;
  type?: string;
  structuredData?: Record<string, unknown>;
  openGraph?: OpenGraphData;
}

export interface OpenGraphData {
  title: string;
  description: string;
  image?: string;
  type: string;
  url?: string;
}

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
}

// Language Types
export type Language = 'sq' | 'en';

export interface LanguageConfig {
  code: Language;
  name: string;
  nativeName: string;
  flag: string;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  children?: NavigationItem[];
  external?: boolean;
}

// Form Types
export interface FormField {
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  label: string;
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule[];
  options?: SelectOption[];
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'phone' | 'minLength' | 'maxLength';
  value?: unknown;
  message: string;
}

// Utility Types
export interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface SearchFilters {
  query?: string;
  category?: string;
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
}

// Loading States
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface PageProps extends BaseComponentProps {
  title?: string;
  description?: string;
}

// Export all types for easy importing
export type * from './index';
