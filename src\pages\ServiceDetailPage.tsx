import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight, Check, ArrowLeft, ChevronDown, ChevronRight, BookOpen, Users, Shield, AlertTriangle, FileText, Settings, Target, Zap, CheckCircle, Search, Eye, Cog, Award, ClipboardList, MessageSquare, TrendingUp, CheckSquare, Mountain, HardHat, GraduationCap, BarChart3, Siren, MapPin, Radio, Zap as Lightning, FileEdit, Beaker, Tag, Database } from 'lucide-react';
import { getServiceById } from '../constants/services';
import Hero from '../components/Hero';
// Images are now served from public folder
const trainingImage = '/images/training.jpg';
const auditsImage = '/images/audit.jpg';
const consultingImage = '/images/consulting.jpg';
const complianceImage = '/images/compliance.jpg';
const emergencyImage = '/images/Emergency.jpg';
const reportingImage = '/images/reporting.jpg';
const sdsImage = '/images/SDS.jpg';
const miningImage = '/images/mining.jpg';
import CtaSection from '../components/CtaSection';

// Icons are now managed centrally in services.ts

interface CaseStudyLink {
  id: string;
  title: string;
  description: string;
}

const serviceImages: { [key: string]: string } = {
  training: trainingImage,
  consulting: consultingImage,
  audits: auditsImage,
  compliance: complianceImage,
  emergency: emergencyImage,
  reporting: reportingImage,
  sds: sdsImage,
  'mining-safety': miningImage,
  // add other mappings as needed
};

const ServiceDetailPage: React.FC = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const { t, i18n } = useTranslation(['services', 'common']);
  const currentLang = i18n.language;
  const [expandedModule, setExpandedModule] = useState<string | null>(null);

  // Function to handle module expansion with smooth scrolling
  const handleModuleToggle = (moduleId: string, event: React.MouseEvent) => {
    const clickedElement = event.currentTarget;

    if (expandedModule === moduleId) {
      // If clicking to close the same module, just close it
      setExpandedModule(null);
    } else {
      // If opening a new module, set it and scroll to it
      setExpandedModule(moduleId);

      // Use setTimeout to ensure the DOM has updated before scrolling
      setTimeout(() => {
        clickedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  };

  const serviceExists = serviceId && i18n.exists(`services:${serviceId}.title`);

  if (!serviceId || !serviceExists) {
    return (
      <div className="min-h-[70vh] flex items-center justify-center px-4 py-12 bg-gray-50">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">{t('service_not_found')}</h1>
          <p className="text-gray-600 mb-8">{t('service_not_found_desc')}</p>
          <Link to={`/${currentLang}/services`} className="btn-primary">
            {t('view_all_services')}
          </Link>
        </div>
      </div>
    );
  }

  // Get service configuration from centralized services.ts
  const serviceConfig = getServiceById(serviceId);

  // Defensively fetch data, providing empty arrays as fallbacks to prevent crashes
  const service = {
    id: serviceId,
    title: t(`${serviceId}.title`),
    subtitle: t(`${serviceId}.subtitle`),
    description: (t(`${serviceId}.description_long`, { defaultValue: "" })).split('\n'),
    benefits: (t(`${serviceId}.benefits`, { returnObjects: true }) as string[]) || [],
    features: (t(`${serviceId}.features`, { returnObjects: true }) as string[]) || [],
    caseStudies: (t(`${serviceId}.caseStudies`, { returnObjects: true }) as CaseStudyLink[]) || [],
    icon: serviceConfig?.icon, // Use centralized icon configuration
    heroImage: serviceImages[serviceId] || trainingImage // fallback to trainingImage if not found
  };

  return (
    <div>
      <Hero title={service.title} subtitle={service.subtitle} ctaText={t('common:header_cta')} ctaLink={`/${currentLang}/contact#formular-kontakti`} bgImage={service.heroImage} />
      <div className="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
        <div className="container mx-auto px-4 md:px-6 py-4">
          <Link to={`/${currentLang}/services`} className="inline-flex items-center text-blue-800 dark:text-blue-300 hover:text-orange-500 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('back_to_services')}
          </Link>
        </div>
      </div>
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col lg:flex-row gap-12 xl:gap-16">
            <div className="lg:w-2/3">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('service_overview')}</h2>
              {service.description.map((p, i) => (<p key={i} className="text-lg text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">{p}</p>))}
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('key_features')}</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">{service.features.map((f, i) => (<li key={i} className="flex items-start"><Check className="w-5 h-5 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" /><span className="dark:text-gray-200">{f}</span></li>))}</ul>
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('your_benefits')}</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">{service.benefits.map((b, i) => (<li key={i} className="flex items-start"><Check className="w-5 h-5 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" /><span className="dark:text-gray-200">{b}</span></li>))}</ul>

              {/* Training Modules Section - Only show for training service */}
              {serviceId === 'training' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('training_modules_title')}</h2>
                  <div className="space-y-6">
                    {/* Basic Compliance Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('basic_compliance', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                              <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('training.training_modules.basic_compliance.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('training_modules_basic_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'basic_compliance' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'basic_compliance' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-4 mt-4">
                            {t('training.training_modules.basic_compliance.description')}
                          </p>
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('topics_covered')}:</h4>
                          <ul className="space-y-2">
                            {(t('training.training_modules.basic_compliance.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                              <li key={index} className="flex items-start">
                                <Check className="w-4 h-4 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{topic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {/* Specialized Risks Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('specialized_risks', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mr-4">
                              <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('training.training_modules.specialized_risks.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('training_modules_risks_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'specialized_risks' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'specialized_risks' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-6 mt-4">
                            {t('training.training_modules.specialized_risks.description')}
                          </p>

                          {/* Risk Categories */}
                          <div className="grid md:grid-cols-2 gap-6">
                            {/* Safety Hazards */}
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                              <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                <Shield className="w-4 h-4 mr-2 text-blue-600" />
                                {t('training.training_modules.specialized_risks.categories.safety_hazards.title')}
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {(t('training.training_modules.specialized_risks.categories.safety_hazards.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                                  <li key={index} className="text-gray-600 dark:text-gray-400">• {topic}</li>
                                ))}
                              </ul>
                            </div>

                            {/* Chemical & Physical */}
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                              <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                <AlertTriangle className="w-4 h-4 mr-2 text-yellow-600" />
                                {t('training.training_modules.specialized_risks.categories.chemical_physical.title')}
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {(t('training.training_modules.specialized_risks.categories.chemical_physical.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                                  <li key={index} className="text-gray-600 dark:text-gray-400">• {topic}</li>
                                ))}
                              </ul>
                            </div>

                            {/* Biological Hazards */}
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                              <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                <Users className="w-4 h-4 mr-2 text-green-600" />
                                {t('training.training_modules.specialized_risks.categories.biological_hazards.title')}
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {(t('training.training_modules.specialized_risks.categories.biological_hazards.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                                  <li key={index} className="text-gray-600 dark:text-gray-400">• {topic}</li>
                                ))}
                              </ul>
                            </div>

                            {/* Ergonomic & Psychosocial */}
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                              <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                <Users className="w-4 h-4 mr-2 text-purple-600" />
                                {t('training.training_modules.specialized_risks.categories.ergonomic_psychosocial.title')}
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {(t('training.training_modules.specialized_risks.categories.ergonomic_psychosocial.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                                  <li key={index} className="text-gray-600 dark:text-gray-400">• {topic}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Emergency & First Aid Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('emergency_first_aid', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4">
                              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('training.training_modules.emergency_first_aid.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('training_modules_emergency_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'emergency_first_aid' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'emergency_first_aid' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-4 mt-4">
                            {t('training.training_modules.emergency_first_aid.description')}
                          </p>
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('topics_covered')}:</h4>
                          <ul className="space-y-2">
                            {(t('training.training_modules.emergency_first_aid.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                              <li key={index} className="flex items-start">
                                <Check className="w-4 h-4 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{topic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {/* Train the Trainer Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('train_the_trainer', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                              <Users className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('training.training_modules.train_the_trainer.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('training_modules_trainer_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'train_the_trainer' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'train_the_trainer' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-4 mt-4">
                            {t('training.training_modules.train_the_trainer.description')}
                          </p>
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('topics_covered')}:</h4>
                          <ul className="space-y-2">
                            {(t('training.training_modules.train_the_trainer.topics', { returnObjects: true }) as string[] || []).map((topic, index) => (
                              <li key={index} className="flex items-start">
                                <Check className="w-4 h-4 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{topic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Consulting Services Section - Only show for consulting service */}
              {serviceId === 'consulting' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('consulting_services_title')}</h2>
                  <div className="space-y-6">
                    {/* VRM Assessment Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('vrm_assessment', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                              <Target className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('consulting.consulting_services.vrm_assessment.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('consulting_vrm_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'vrm_assessment' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'vrm_assessment' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-6 mt-4">
                            {t('consulting.consulting_services.vrm_assessment.description')}
                          </p>

                          {/* Process Steps */}
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('vrm_process_title')}:</h4>
                          <div className="space-y-4 mb-6">
                            {(t('consulting.consulting_services.vrm_assessment.process_steps', { returnObjects: true }) as any[] || []).map((step, index) => (
                              <div key={index} className="flex items-start bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-4 flex-shrink-0 font-bold text-sm">
                                  {step.step}
                                </div>
                                <div>
                                  <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">{step.title}</h5>
                                  <p className="text-gray-600 dark:text-gray-400 text-sm">{step.description}</p>
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Deliverables */}
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                          <ul className="space-y-2">
                            {(t('consulting.consulting_services.vrm_assessment.deliverables', { returnObjects: true }) as string[] || []).map((deliverable, index) => (
                              <li key={index} className="flex items-start">
                                <Check className="w-4 h-4 text-green-600 dark:text-green-400 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{deliverable}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {/* Safety Programs Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('safety_programs', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                              <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('consulting.consulting_services.safety_programs.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('consulting_programs_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'safety_programs' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'safety_programs' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-6 mt-4">
                            {t('consulting.consulting_services.safety_programs.description')}
                          </p>

                          {/* Program Components */}
                          <div className="grid md:grid-cols-2 gap-4">
                            {(t('consulting.consulting_services.safety_programs.components', { returnObjects: true }) as any[] || []).map((component, index) => (
                              <div key={index} className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">{component.title}</h5>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">{component.description}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Emergency Planning Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('emergency_planning', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4">
                              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('consulting.consulting_services.emergency_planning.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('consulting_emergency_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'emergency_planning' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'emergency_planning' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-6 mt-4">
                            {t('consulting.consulting_services.emergency_planning.description')}
                          </p>

                          {/* Planning Process */}
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('planning_process_title')}:</h4>
                          <div className="space-y-3">
                            {(t('consulting.consulting_services.emergency_planning.planning_process', { returnObjects: true }) as any[] || []).map((phase, index) => (
                              <div key={index} className="flex items-start bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div className="w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center mr-3 flex-shrink-0 text-xs font-bold">
                                  {index + 1}
                                </div>
                                <div>
                                  <h5 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">{phase.phase}</h5>
                                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{phase.description}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Implementation Support Module */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <button
                        onClick={(e) => handleModuleToggle('implementation_support', e)}
                        className="w-full p-6 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4">
                              <Settings className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                {t('consulting.consulting_services.implementation_support.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('consulting_implementation_subtitle')}
                              </p>
                            </div>
                          </div>
                          {expandedModule === 'implementation_support' ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>
                      {expandedModule === 'implementation_support' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-gray-700 dark:text-gray-300 mb-4 mt-4">
                            {t('consulting.consulting_services.implementation_support.description')}
                          </p>

                          {/* Support Areas */}
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('support_areas_title')}:</h4>
                          <ul className="space-y-2">
                            {(t('consulting.consulting_services.implementation_support.support_areas', { returnObjects: true }) as string[] || []).map((area, index) => (
                              <li key={index} className="flex items-start">
                                <Zap className="w-4 h-4 text-purple-600 dark:text-purple-400 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{area}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Auditing Modules Section - Only show for audits service */}
              {serviceId === 'audits' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('audit_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* Compliance Audit Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('compliance_audit', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <CheckCircle className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:audits.audit_modules.compliance_audit.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('audit_compliance_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'compliance_audit' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'compliance_audit' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:audits.audit_modules.compliance_audit.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('audit_areas_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:audits.audit_modules.compliance_audit.audit_areas', { returnObjects: true }) as string[]).map((area: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{area}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:audits.audit_modules.compliance_audit.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* SMS Audit Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('sms_audit', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Settings className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:audits.audit_modules.sms_audit.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('audit_sms_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'sms_audit' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'sms_audit' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:audits.audit_modules.sms_audit.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('evaluation_criteria_title')}:</h4>
                            <div className="space-y-4">
                              {(t('services:audits.audit_modules.sms_audit.evaluation_criteria', { returnObjects: true }) as Array<{area: string, description: string}>).map((criteria, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{criteria.area}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{criteria.description}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Workplace Inspection Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('workplace_inspection', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Search className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:audits.audit_modules.workplace_inspection.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('audit_inspection_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'workplace_inspection' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'workplace_inspection' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:audits.audit_modules.workplace_inspection.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('inspection_scope_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:audits.audit_modules.workplace_inspection.inspection_scope', { returnObjects: true }) as string[]).map((scope: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Eye className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{scope}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('methodology_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:audits.audit_modules.workplace_inspection.methodology', { returnObjects: true }) as string[]).map((method: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Cog className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{method}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Documentation Audit Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('documentation_audit', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <FileText className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:audits.audit_modules.documentation_audit.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('audit_documentation_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'documentation_audit' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'documentation_audit' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:audits.audit_modules.documentation_audit.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('document_categories_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:audits.audit_modules.documentation_audit.document_categories', { returnObjects: true }) as string[]).map((category: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{category}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('review_criteria_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:audits.audit_modules.documentation_audit.review_criteria', { returnObjects: true }) as string[]).map((criteria: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{criteria}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Compliance Modules Section - Only show for compliance service */}
              {serviceId === 'compliance' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('compliance_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* Regulatory Identification Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('regulatory_identification', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Search className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:compliance.compliance_modules.regulatory_identification.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('compliance_regulatory_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'regulatory_identification' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'regulatory_identification' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:compliance.compliance_modules.regulatory_identification.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('scope_areas_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:compliance.compliance_modules.regulatory_identification.scope_areas', { returnObjects: true }) as string[]).map((area: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{area}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:compliance.compliance_modules.regulatory_identification.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Gap Assessment Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('gap_assessment', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Target className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:compliance.compliance_modules.gap_assessment.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('compliance_gap_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'gap_assessment' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'gap_assessment' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:compliance.compliance_modules.gap_assessment.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('assessment_process_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:compliance.compliance_modules.gap_assessment.assessment_process', { returnObjects: true }) as Array<{phase: string, description: string}>).map((process, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{process.phase}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{process.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('outcomes_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:compliance.compliance_modules.gap_assessment.outcomes', { returnObjects: true }) as string[]).map((outcome: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{outcome}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Management Systems Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('management_systems', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Settings className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:compliance.compliance_modules.management_systems.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('compliance_systems_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'management_systems' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'management_systems' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:compliance.compliance_modules.management_systems.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('system_components_title')}:</h4>
                            <div className="space-y-4">
                              {(t('services:compliance.compliance_modules.management_systems.system_components', { returnObjects: true }) as Array<{component: string, description: string}>).map((component, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{component.component}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{component.description}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Training & Certification Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('training_certification', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Award className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:compliance.compliance_modules.training_certification.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('compliance_training_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'training_certification' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'training_certification' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:compliance.compliance_modules.training_certification.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('training_areas_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:compliance.compliance_modules.training_certification.training_areas', { returnObjects: true }) as string[]).map((area: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Award className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{area}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Reporting Modules Section - Only show for reporting service */}
              {serviceId === 'reporting' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('reporting_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* Reporting Systems Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('reporting_systems', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <ClipboardList className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:reporting.reporting_modules.reporting_systems.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('reporting_systems_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'reporting_systems' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'reporting_systems' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:reporting.reporting_modules.reporting_systems.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('system_features_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:reporting.reporting_modules.reporting_systems.system_features', { returnObjects: true }) as string[]).map((feature: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('implementation_steps_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:reporting.reporting_modules.reporting_systems.implementation_steps', { returnObjects: true }) as string[]).map((step: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <ArrowRight className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{step}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Investigation Protocols Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('investigation_protocols', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <MessageSquare className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:reporting.reporting_modules.investigation_protocols.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('investigation_protocols_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'investigation_protocols' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'investigation_protocols' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:reporting.reporting_modules.investigation_protocols.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('protocol_elements_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:reporting.reporting_modules.investigation_protocols.protocol_elements', { returnObjects: true }) as Array<{phase: string, description: string}>).map((element, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{element.phase}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{element.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('investigation_tools_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:reporting.reporting_modules.investigation_protocols.investigation_tools', { returnObjects: true }) as string[]).map((tool: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{tool}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Root Cause Analysis Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('root_cause_analysis', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <TrendingUp className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:reporting.reporting_modules.root_cause_analysis.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('root_cause_analysis_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'root_cause_analysis' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'root_cause_analysis' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:reporting.reporting_modules.root_cause_analysis.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('analysis_methods_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:reporting.reporting_modules.root_cause_analysis.analysis_methods', { returnObjects: true }) as Array<{method: string, description: string}>).map((method, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{method.method}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{method.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:reporting.reporting_modules.root_cause_analysis.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Corrective Actions Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('corrective_actions', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <CheckSquare className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:reporting.reporting_modules.corrective_actions.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('corrective_actions_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'corrective_actions' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'corrective_actions' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:reporting.reporting_modules.corrective_actions.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('tracking_components_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:reporting.reporting_modules.corrective_actions.tracking_components', { returnObjects: true }) as string[]).map((component: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{component}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('success_metrics_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:reporting.reporting_modules.corrective_actions.success_metrics', { returnObjects: true }) as string[]).map((metric: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <TrendingUp className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{metric}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Mining Safety Modules Section - Only show for mining-safety service */}
              {serviceId === 'mining-safety' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('mining_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* Risk Assessment Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('mining_risk_assessment', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Mountain className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:mining-safety.mining_modules.risk_assessment.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('mining_risk_assessment_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'mining_risk_assessment' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'mining_risk_assessment' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:mining-safety.mining_modules.risk_assessment.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('assessment_areas_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:mining-safety.mining_modules.risk_assessment.assessment_areas', { returnObjects: true }) as string[]).map((area: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{area}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('methodologies_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:mining-safety.mining_modules.risk_assessment.methodologies', { returnObjects: true }) as Array<{method: string, description: string}>).map((methodology, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{methodology.method}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{methodology.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:mining-safety.mining_modules.risk_assessment.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Safety Programs Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('mining_safety_programs', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <HardHat className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:mining-safety.mining_modules.safety_programs.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('mining_safety_programs_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'mining_safety_programs' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'mining_safety_programs' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:mining-safety.mining_modules.safety_programs.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('program_components_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:mining-safety.mining_modules.safety_programs.program_components', { returnObjects: true }) as Array<{component: string, description: string}>).map((component, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{component.component}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{component.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('implementation_phases_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:mining-safety.mining_modules.safety_programs.implementation_phases', { returnObjects: true }) as string[]).map((phase: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <ArrowRight className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{phase}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Specialized Training Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('mining_specialized_training', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <GraduationCap className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:mining-safety.mining_modules.specialized_training.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('mining_specialized_training_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'mining_specialized_training' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'mining_specialized_training' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:mining-safety.mining_modules.specialized_training.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('training_categories_title')}:</h4>
                            <div className="space-y-6 mb-6">
                              {(t('services:mining-safety.mining_modules.specialized_training.training_categories', { returnObjects: true }) as Array<{category: string, description: string, topics: string[]}>).map((category, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{category.category}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">{category.description}</p>
                                  <ul className="space-y-1">
                                    {category.topics.map((topic: string, topicIndex: number) => (
                                      <li key={topicIndex} className="flex items-start space-x-2">
                                        <Check className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                                        <span className="text-gray-600 dark:text-gray-300 text-sm">{topic}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('certification_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:mining-safety.mining_modules.specialized_training.certification', { returnObjects: true }) as string[]).map((cert: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Award className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{cert}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Monitoring and Auditing Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('mining_monitoring_auditing', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:mining-safety.mining_modules.monitoring_auditing.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('mining_monitoring_auditing_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'mining_monitoring_auditing' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'mining_monitoring_auditing' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:mining-safety.mining_modules.monitoring_auditing.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('monitoring_systems_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:mining-safety.mining_modules.monitoring_auditing.monitoring_systems', { returnObjects: true }) as string[]).map((system: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{system}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('audit_types_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:mining-safety.mining_modules.monitoring_auditing.audit_types', { returnObjects: true }) as Array<{type: string, description: string}>).map((auditType, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{auditType.type}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{auditType.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('reporting_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:mining-safety.mining_modules.monitoring_auditing.reporting', { returnObjects: true }) as string[]).map((report: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{report}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Emergency Planning Modules Section - Only show for emergency service */}
              {serviceId === 'emergency' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('emergency_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* Hazard Assessment Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('emergency_hazard_assessment', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Siren className="h-8 w-8 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:emergency.emergency_modules.hazard_assessment.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('emergency_hazard_assessment_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'emergency_hazard_assessment' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'emergency_hazard_assessment' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:emergency.emergency_modules.hazard_assessment.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('hazard_categories_title')}:</h4>
                            <div className="space-y-6 mb-6">
                              {(t('services:emergency.emergency_modules.hazard_assessment.hazard_categories', { returnObjects: true }) as Array<{category: string, hazards: string[]}>).map((category, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">{category.category}</h5>
                                  <ul className="space-y-1">
                                    {category.hazards.map((hazard: string, hazardIndex: number) => (
                                      <li key={hazardIndex} className="flex items-start space-x-2">
                                        <AlertTriangle className="h-3 w-3 text-red-500 mt-1 flex-shrink-0" />
                                        <span className="text-gray-600 dark:text-gray-300 text-sm">{hazard}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('assessment_methodology_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:emergency.emergency_modules.hazard_assessment.assessment_methodology', { returnObjects: true }) as string[]).map((method: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{method}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:emergency.emergency_modules.hazard_assessment.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Response Plans Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('emergency_response_plans', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <MapPin className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:emergency.emergency_modules.response_plans.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('emergency_response_plans_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'emergency_response_plans' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'emergency_response_plans' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:emergency.emergency_modules.response_plans.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('plan_components_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:emergency.emergency_modules.response_plans.plan_components', { returnObjects: true }) as Array<{component: string, description: string}>).map((component, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{component.component}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{component.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('plan_types_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:emergency.emergency_modules.response_plans.plan_types', { returnObjects: true }) as string[]).map((planType: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Shield className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{planType}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('development_process_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:emergency.emergency_modules.response_plans.development_process', { returnObjects: true }) as string[]).map((step: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <ArrowRight className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{step}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Evacuation and Communication Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('emergency_evacuation_communication', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Radio className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:emergency.emergency_modules.evacuation_communication.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('emergency_evacuation_communication_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'emergency_evacuation_communication' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'emergency_evacuation_communication' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:emergency.emergency_modules.evacuation_communication.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('evacuation_procedures_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:emergency.emergency_modules.evacuation_communication.evacuation_procedures', { returnObjects: true }) as Array<{procedure: string, description: string}>).map((procedure, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{procedure.procedure}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{procedure.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('communication_systems_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:emergency.emergency_modules.evacuation_communication.communication_systems', { returnObjects: true }) as string[]).map((system: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Radio className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{system}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('signage_equipment_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:emergency.emergency_modules.evacuation_communication.signage_equipment', { returnObjects: true }) as string[]).map((equipment: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Settings className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{equipment}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Drills and Training Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('emergency_drills_training', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Lightning className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:emergency.emergency_modules.drills_training.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('emergency_drills_training_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'emergency_drills_training' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'emergency_drills_training' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:emergency.emergency_modules.drills_training.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('drill_types_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:emergency.emergency_modules.drills_training.drill_types', { returnObjects: true }) as Array<{type: string, description: string, frequency: string}>).map((drill, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{drill.type}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">{drill.description}</p>
                                  <p className="text-gray-500 dark:text-gray-400 text-xs">
                                    <strong>Frequency:</strong> {drill.frequency}
                                  </p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('training_programs_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:emergency.emergency_modules.drills_training.training_programs', { returnObjects: true }) as string[]).map((program: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <GraduationCap className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{program}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('evaluation_improvement_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:emergency.emergency_modules.drills_training.evaluation_improvement', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <TrendingUp className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{item}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* SDS Modules Section - Only show for sds service */}
              {serviceId === 'sds' && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('sds_modules_title')}</h2>

                  <div className="grid gap-6">
                    {/* SDS Authoring Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('sds_authoring', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <FileEdit className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:sds.sds_modules.sds_authoring.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('sds_authoring_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'sds_authoring' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'sds_authoring' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:sds.sds_modules.sds_authoring.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('authoring_process_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:sds.sds_modules.sds_authoring.authoring_process', { returnObjects: true }) as Array<{step: string, description: string}>).map((process, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{process.step}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{process.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('sds_sections_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:sds.sds_modules.sds_authoring.sds_sections', { returnObjects: true }) as string[]).map((section: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{section}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('quality_standards_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:sds.sds_modules.sds_authoring.quality_standards', { returnObjects: true }) as string[]).map((standard: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{standard}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Hazard Classification Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('sds_hazard_classification', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Beaker className="h-8 w-8 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:sds.sds_modules.hazard_classification.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('sds_hazard_classification_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'sds_hazard_classification' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'sds_hazard_classification' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:sds.sds_modules.hazard_classification.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('hazard_categories_title')}:</h4>
                            <div className="space-y-6 mb-6">
                              {(t('services:sds.sds_modules.hazard_classification.hazard_categories', { returnObjects: true }) as Array<{category: string, hazards: string[]}>).map((category, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">{category.category}</h5>
                                  <ul className="space-y-1">
                                    {category.hazards.map((hazard: string, hazardIndex: number) => (
                                      <li key={hazardIndex} className="flex items-start space-x-2">
                                        <AlertTriangle className="h-3 w-3 text-red-500 mt-1 flex-shrink-0" />
                                        <span className="text-gray-600 dark:text-gray-300 text-sm">{hazard}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('classification_process_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:sds.sds_modules.hazard_classification.classification_process', { returnObjects: true }) as string[]).map((process: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <ArrowRight className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{process}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('deliverables_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:sds.sds_modules.hazard_classification.deliverables', { returnObjects: true }) as string[]).map((deliverable: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <FileText className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Labeling and Pictograms Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('sds_labeling_pictograms', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Tag className="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:sds.sds_modules.labeling_pictograms.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('sds_labeling_pictograms_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'sds_labeling_pictograms' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'sds_labeling_pictograms' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:sds.sds_modules.labeling_pictograms.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('labeling_elements_title')}:</h4>
                            <div className="space-y-4 mb-6">
                              {(t('services:sds.sds_modules.labeling_pictograms.labeling_elements', { returnObjects: true }) as Array<{element: string, description: string}>).map((element, index: number) => (
                                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{element.element}</h5>
                                  <p className="text-gray-600 dark:text-gray-300 text-sm">{element.description}</p>
                                </div>
                              ))}
                            </div>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('pictogram_types_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:sds.sds_modules.labeling_pictograms.pictogram_types', { returnObjects: true }) as string[]).map((pictogram: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Tag className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{pictogram}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('compliance_requirements_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:sds.sds_modules.labeling_pictograms.compliance_requirements', { returnObjects: true }) as string[]).map((requirement: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{requirement}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Data Management Module */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div
                        className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                        onClick={(e) => handleModuleToggle('sds_data_management', e)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <Database className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('services:sds.sds_modules.data_management.title')}
                              </h3>
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {t('sds_data_management_subtitle')}
                              </p>
                            </div>
                          </div>
                          <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${expandedModule === 'sds_data_management' ? 'rotate-180' : ''}`} />
                        </div>
                      </div>

                      {expandedModule === 'sds_data_management' && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {t('services:sds.sds_modules.data_management.description')}
                            </p>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('management_features_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:sds.sds_modules.data_management.management_features', { returnObjects: true }) as string[]).map((feature: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Database className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('tracking_capabilities_title')}:</h4>
                            <ul className="space-y-2 mb-6">
                              {(t('services:sds.sds_modules.data_management.tracking_capabilities', { returnObjects: true }) as string[]).map((capability: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <BarChart3 className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{capability}</span>
                                </li>
                              ))}
                            </ul>

                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('maintenance_services_title')}:</h4>
                            <ul className="space-y-2">
                              {(t('services:sds.sds_modules.data_management.maintenance_services', { returnObjects: true }) as string[]).map((service: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <Settings className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{service}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Only show case studies section if there are actual case studies */}
              {service.caseStudies && service.caseStudies.length > 0 && (
                <>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-12 mb-6">{t('related_case_studies')}</h2>
                  <div className="space-y-6">
                    {service.caseStudies.map((cs) => (
                      <div key={cs.id} className="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">{cs.title}</h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed">{cs.description}</p>
                        <Link to={`/${currentLang}/case-studies/${cs.id}`} className="inline-flex items-center text-blue-700 dark:text-blue-300 font-medium hover:text-orange-600 transition-colors group text-sm">
                          {t('common:button_readFullCaseStudy')}
                          <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </Link>
                      </div>
                    ))}
                    <div className="pt-4 text-center">
                      <Link to={`/${currentLang}/case-studies`} className="text-blue-800 dark:text-blue-300 hover:text-orange-500 font-medium">
                        {t('view_all_case_studies')}
                      </Link>
                    </div>
                  </div>
                </>
              )}
            </div>
            <aside className="lg:w-1/3">
              <div className="bg-blue-50 dark:bg-gray-900 rounded-lg border border-blue-200 dark:border-gray-700 p-6 sticky top-24 shadow-sm">
                <div className="text-center mb-6 pb-6 border-b border-blue-200 dark:border-gray-700">
                  <div className="inline-block p-4 bg-blue-100 dark:bg-gray-800 rounded-full mb-4 shadow">
                    {/* Render icon component with Tailwind classes for light/dark mode */}
                    {service.icon &&
                      React.createElement(service.icon, {
                        className: "w-10 h-10 text-blue-800 dark:text-blue-300",
                        'aria-hidden': true
                      })}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{service.title}</h3>
                </div>
                <div className="space-y-4 mb-6 text-center">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{t('ready_to_improve_safety')}</p>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{t('contact_us_today')}</p>
                </div>
                <Link to={`/${currentLang}/contact#formular-kontakti`} className="btn-primary w-full text-center block mb-6 py-3">{t('request_free_consultation')}</Link>
                <div className="mt-6 pt-6 border-t border-blue-200 dark:border-gray-700 text-center">
                  <p className="font-semibold text-gray-800 dark:text-gray-100 mb-3 text-lg">{t('have_questions')}</p>
                  <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm">{t('call_directly')}</p>
                  <a href="tel:+38344123456" className="text-blue-800 dark:text-blue-300 font-semibold text-lg hover:text-orange-600 transition-colors">+383.44.819.701</a>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">{t('or_email_us')} <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-300 hover:underline">{t('send_email')}</a></p>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </section>
      <CtaSection title={t('ready_to_improve_with_service')} description={t('contact_today_desc')} ctaText={t('common:header_cta')} ctaLink={`/${currentLang}/contact#formular-kontakti`} />
    </div>
  );
};

export default ServiceDetailPage;