import { getDatabase } from './database.js';

export interface Migration {
  version: number;
  name: string;
  up: (db: any) => void;
  down: (db: any) => void;
}

// Migration tracking table
const MIGRATIONS_TABLE = `
  CREATE TABLE IF NOT EXISTS migrations (
    version INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`;

/**
 * Available migrations
 */
const migrations: Migration[] = [
  {
    version: 1,
    name: 'initial_schema',
    up: (db) => {
      // Create inquiries table
      db.exec(`
        CREATE TABLE IF NOT EXISTS inquiries (
          id TEXT PRIMARY KEY,
          contact_name TEXT NOT NULL,
          contact_email TEXT NOT NULL,
          company_name TEXT,
          inquiry_category TEXT NOT NULL,
          inquiry_subcategory TEXT,
          urgency_level TEXT NOT NULL,
          description TEXT NOT NULL,
          status TEXT DEFAULT 'new',
          assigned_to TEXT,
          requires_professional INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create resources table
      db.exec(`
        CREATE TABLE IF NOT EXISTS resources (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL,
          type TEXT NOT NULL,
          file_url TEXT NOT NULL,
          preview_url TEXT,
          file_size INTEGER,
          format TEXT,
          language TEXT DEFAULT 'sq',
          download_count INTEGER DEFAULT 0,
          disclaimer TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create user_activities table
      db.exec(`
        CREATE TABLE IF NOT EXISTS user_activities (
          id TEXT PRIMARY KEY,
          session_id TEXT,
          user_email TEXT,
          ip_address TEXT,
          activity_type TEXT NOT NULL,
          resource_id TEXT,
          inquiry_id TEXT,
          metadata TEXT,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (resource_id) REFERENCES resources(id),
          FOREIGN KEY (inquiry_id) REFERENCES inquiries(id)
        )
      `);
    },
    down: (db) => {
      db.exec('DROP TABLE IF EXISTS user_activities');
      db.exec('DROP TABLE IF EXISTS resources');
      db.exec('DROP TABLE IF EXISTS inquiries');
    }
  },
  {
    version: 2,
    name: 'add_indexes',
    up: (db) => {
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_inquiries_status ON inquiries(status);
        CREATE INDEX IF NOT EXISTS idx_inquiries_category ON inquiries(inquiry_category);
        CREATE INDEX IF NOT EXISTS idx_inquiries_created_at ON inquiries(created_at);
        CREATE INDEX IF NOT EXISTS idx_resources_category ON resources(category);
        CREATE INDEX IF NOT EXISTS idx_resources_type ON resources(type);
        CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
        CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);
      `);
    },
    down: (db) => {
      db.exec('DROP INDEX IF EXISTS idx_inquiries_status');
      db.exec('DROP INDEX IF EXISTS idx_inquiries_category');
      db.exec('DROP INDEX IF EXISTS idx_inquiries_created_at');
      db.exec('DROP INDEX IF EXISTS idx_resources_category');
      db.exec('DROP INDEX IF EXISTS idx_resources_type');
      db.exec('DROP INDEX IF EXISTS idx_user_activities_type');
      db.exec('DROP INDEX IF EXISTS idx_user_activities_timestamp');
    }
  }
];

/**
 * Initialize migrations table
 */
function initMigrationsTable(): void {
  const db = getDatabase();
  db.exec(MIGRATIONS_TABLE);
}

/**
 * Get applied migrations
 */
function getAppliedMigrations(): number[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT version FROM migrations ORDER BY version');
  const rows = stmt.all() as { version: number }[];
  return rows.map(row => row.version);
}

/**
 * Mark migration as applied
 */
function markMigrationApplied(migration: Migration): void {
  const db = getDatabase();
  const stmt = db.prepare('INSERT INTO migrations (version, name) VALUES (?, ?)');
  stmt.run(migration.version, migration.name);
}

/**
 * Mark migration as reverted
 */
function markMigrationReverted(version: number): void {
  const db = getDatabase();
  const stmt = db.prepare('DELETE FROM migrations WHERE version = ?');
  stmt.run(version);
}

/**
 * Run pending migrations
 */
export function runMigrations(): void {
  initMigrationsTable();
  
  const appliedVersions = getAppliedMigrations();
  const pendingMigrations = migrations.filter(
    migration => !appliedVersions.includes(migration.version)
  );

  if (pendingMigrations.length === 0) {
    console.log('No pending migrations');
    return;
  }

  const db = getDatabase();
  
  for (const migration of pendingMigrations) {
    console.log(`Running migration ${migration.version}: ${migration.name}`);
    
    try {
      db.transaction(() => {
        migration.up(db);
        markMigrationApplied(migration);
      })();
      
      console.log(`Migration ${migration.version} completed successfully`);
    } catch (error) {
      console.error(`Migration ${migration.version} failed:`, error);
      throw error;
    }
  }
}

/**
 * Rollback migrations to a specific version
 */
export function rollbackMigrations(targetVersion: number): void {
  initMigrationsTable();
  
  const appliedVersions = getAppliedMigrations();
  const migrationsToRollback = migrations
    .filter(migration => 
      appliedVersions.includes(migration.version) && 
      migration.version > targetVersion
    )
    .sort((a, b) => b.version - a.version); // Rollback in reverse order

  if (migrationsToRollback.length === 0) {
    console.log('No migrations to rollback');
    return;
  }

  const db = getDatabase();
  
  for (const migration of migrationsToRollback) {
    console.log(`Rolling back migration ${migration.version}: ${migration.name}`);
    
    try {
      db.transaction(() => {
        migration.down(db);
        markMigrationReverted(migration.version);
      })();
      
      console.log(`Migration ${migration.version} rolled back successfully`);
    } catch (error) {
      console.error(`Rollback of migration ${migration.version} failed:`, error);
      throw error;
    }
  }
}

/**
 * Get migration status
 */
export function getMigrationStatus(): { version: number; name: string; applied: boolean }[] {
  initMigrationsTable();
  
  const appliedVersions = getAppliedMigrations();
  
  return migrations.map(migration => ({
    version: migration.version,
    name: migration.name,
    applied: appliedVersions.includes(migration.version)
  }));
}