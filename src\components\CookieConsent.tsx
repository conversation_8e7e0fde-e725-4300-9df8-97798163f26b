import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';

const CookieConsent: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { t, i18n } = useTranslation('cookies');
  const currentLang = i18n.language;

  useEffect(() => {
    const hasConsent = localStorage.getItem('cookie-consent');
    if (!hasConsent) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1500); // Reduced delay for better UX
      return () => clearTimeout(timer);
    }
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    if (isVisible) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsVisible(false);
        }
      };
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isVisible]);

  const acceptCookies = () => {
    localStorage.setItem('cookie-consent', 'true');
    setIsVisible(false);
  };

  const declineCookies = () => {
    localStorage.setItem('cookie-consent', 'false');
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-900 to-blue-800 dark:from-gray-900 dark:to-gray-800 shadow-lg z-50 p-4 md:p-6 border-t border-blue-700 dark:border-gray-700">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2 text-white">
              {t('title')}
            </h3>
            <p className="text-blue-100 dark:text-gray-300 text-sm">
              {t('description')}{' '}
              <Link 
                to={`/${currentLang}/privacy`}
                className="text-orange-300 hover:text-orange-200 underline font-medium"
              >
                {t('privacy_policy')}
              </Link>
            </p>
          </div>
          <div className="flex gap-3">
            <button 
              onClick={declineCookies}
              className="px-4 py-2 border border-blue-300 rounded-md text-blue-100 hover:bg-blue-700 hover:border-blue-200 transition-colors"
            >
              {t('decline')}
            </button>
            <button 
              onClick={acceptCookies}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors font-medium"
            >
              {t('accept')}
            </button>
          </div>
          <button 
            onClick={() => setIsVisible(false)}
            className="absolute top-2 right-2 text-blue-200 hover:text-white transition-colors"
            aria-label={t('close')}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
