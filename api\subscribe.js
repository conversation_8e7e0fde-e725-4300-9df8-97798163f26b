// Vercel Serverless Function for Newsletter Subscription
import { Resend } from 'resend';

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Email validation function
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

// HTML escape function for security
function escapeHtml(text) {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Get email recipients
function getEmailRecipients() {
  const recipients = process.env.EMAIL_RECIPIENTS || '<EMAIL>';
  return recipients.split(',').map(email => email.trim());
}

// Rate limiting storage (in-memory for simplicity)
const rateLimitStore = new Map();

function checkRateLimit(ip, limit = 10, windowMs = 60 * 60 * 1000) {
  const now = Date.now();
  const key = `subscribe_${ip}`;
  
  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const record = rateLimitStore.get(key);
  
  if (now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (record.count >= limit) {
    return false;
  }
  
  record.count++;
  return true;
}

export default async function handler(req, res) {
  try {
    console.log('🚀 Subscribe API function started');
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      console.log('✅ Handling OPTIONS request');
      return res.status(200).end();
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
      console.log('❌ Method not allowed:', req.method);
      return res.status(405).json({ error: 'Method not allowed' });
    }
    console.log('📧 Newsletter subscription request received');
    console.log('Request body:', req.body);
    console.log('Request method:', req.method);
    console.log('Request headers:', req.headers);

    // Check if body exists
    if (!req.body) {
      return res.status(400).json({
        error: 'No request body provided'
      });
    }

    // Rate limiting
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
    if (!checkRateLimit(clientIP)) {
      return res.status(429).json({ 
        error: 'Too many subscription attempts, please try again later.' 
      });
    }

    const { email, language } = req.body;

    // Validation
    if (!email || !isValidEmail(email)) {
      return res.status(400).json({
        error: 'Invalid email address',
        details: ['Please provide a valid email address']
      });
    }

    // Check if Resend API key is configured
    if (!process.env.RESEND_API_KEY) {
      console.error('❌ Resend API key not configured');
      return res.status(500).json({ message: 'Email service not configured' });
    }

    const recipients = getEmailRecipients();
    const safeEmail = escapeHtml(email.trim());

    // Send notification email to admin
    const { error: adminError } = await resend.emails.send({
      from: 'Newsletter <<EMAIL>>',
      to: recipients,
      subject: 'New Newsletter Subscription - SafeWork Kosova',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Newsletter Subscription</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Header -->
            <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 30px 20px; text-align: center;">
              <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">SafeWork Kosova</h1>
              <p style="color: #dbeafe; margin: 10px 0 0 0; font-size: 16px;">New Newsletter Subscription</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px 20px;">
              <div style="background-color: #dcfce7; border-left: 4px solid #16a34a; padding: 15px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
                <p style="margin: 0; color: #15803d; font-weight: 600;">📧 New newsletter subscriber!</p>
              </div>
              
              <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 22px;">Subscription Details</h2>
              
              <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px; background-color: #f9fafb; border-radius: 8px; overflow: hidden;">
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6; width: 30%;">📧 Email:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;"><a href="mailto:${safeEmail}" style="color: #2563eb; text-decoration: none;">${safeEmail}</a></td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">🌐 Language:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">
                    <span style="background-color: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500;">${language || 'Not specified'}</span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">⏰ Subscribed:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${new Date().toLocaleString()}</td>
                </tr>
                <tr>
                  <td style="padding: 15px; font-weight: 600; color: #374151; background-color: #f3f4f6;">🌍 IP Address:</td>
                  <td style="padding: 15px; color: #1f2937;">${clientIP}</td>
                </tr>
              </table>
              
              <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px;">
                <h3 style="margin: 0 0 10px 0; color: #0c4a6e; font-size: 16px;">📊 Next Steps</h3>
                <p style="margin: 0; color: #0c4a6e; font-size: 14px;">
                  • Add subscriber to your newsletter list<br>
                  • Send welcome email (automated)<br>
                  • Include in future safety updates and newsletters
                </p>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
              <p style="margin: 0; color: #6b7280; font-size: 12px;">
                This is an automated notification from SafeWork Kosova newsletter subscription.<br>
                The subscriber will receive a welcome email automatically.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    });

    if (adminError) {
      console.error('❌ Failed to send admin notification:', adminError);
      return res.status(500).json({ 
        message: 'Failed to process subscription. Please try again later.' 
      });
    }

    // Prepare email content based on language
    let subject = '';
    let htmlBody = '';
    let responseMessage = '';

    switch (language) {
      case 'en':
        subject = 'Welcome to SafeWork Kosova Newsletter!';
        htmlBody = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to SafeWork Kosova Newsletter</title>
          </head>
          <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
              <!-- Header -->
              <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
                <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Your Partner in Safety Excellence</p>
              </div>
              
              <!-- Content -->
              <div style="padding: 40px 30px;">
                <div style="text-align: center; margin-bottom: 30px;">
                  <table style="margin: 0 auto 20px auto;">
                    <tr>
                      <td style="background-color: #16a34a; color: white; width: 60px; height: 60px; border-radius: 30px; text-align: center; vertical-align: middle; font-size: 24px; font-weight: bold;">📧</td>
                    </tr>
                  </table>
                  <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Welcome to Our Newsletter!</h2>
                  <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">Thank you for joining our safety community</p>
                </div>
                
                <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
                  <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 What You'll Receive</h3>
                  <ul style="margin: 0; padding-left: 20px; color: #0c4a6e; line-height: 1.8;">
                    <li>🛡️ Workplace safety tips and best practices</li>
                    <li>📋 New safety regulations and compliance updates</li>
                    <li>🎓 Training opportunities and workshops</li>
                    <li>📰 Industry news and safety insights</li>
                    <li>💡 Expert advice from our safety professionals</li>
                  </ul>
                </div>
                
                <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
                  <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">🎯 Our Commitment</h3>
                  <p style="margin: 0; color: #92400e; line-height: 1.6;">
                    We're committed to helping you maintain a safe and healthy workplace. Our newsletter provides practical, actionable safety information you can implement immediately.
                  </p>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                  <p style="color: #6b7280; margin: 0 0 20px 0; font-size: 16px;">Start exploring our safety resources:</p>
                  <a href="https://safework-kosova.com/en/services" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Our Services</a>
                  <a href="https://safework-kosova.com/en/blog" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Safety Blog</a>
                </div>
              </div>
              
              <!-- Contact Info -->
              <div style="background-color: #1f2937; padding: 30px 20px; text-align: center;">
                <h3 style="color: #ffffff; margin: 0 0 20px 0; font-size: 20px;">Contact Information</h3>
                <div style="display: inline-block; text-align: left;">
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📧 Email:</strong> 
                    <a href="mailto:<EMAIL>" style="color: #60a5fa; text-decoration: none;"><EMAIL></a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📞 Phone:</strong> 
                    <a href="tel:+38344819701" style="color: #60a5fa; text-decoration: none;">+383 44 819 701</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">🌐 Website:</strong> 
                    <a href="https://safework-kosova.com" style="color: #60a5fa; text-decoration: none;">www.safework-kosova.com</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📍 Address:</strong> Fatmir Ratkoceri Street, Ferizaj 70000, Kosovo
                  </p>
                </div>
              </div>
              
              <!-- Footer -->
              <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="margin: 0; color: #6b7280; font-size: 12px;">
                  If you didn't subscribe to this newsletter, please ignore this email.<br>
                  You can unsubscribe at any time by contacting <NAME_EMAIL>
                </p>
              </div>
            </div>
          </body>
          </html>
        `;
        responseMessage = 'Successfully subscribed to newsletter! Check your email for confirmation.';
        break;
      default: // Albanian
        subject = 'Mirë se vini në Buletinin e SafeWork Kosova!';
        htmlBody = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Mirë se vini në Buletinin e SafeWork Kosova</title>
          </head>
          <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
              <!-- Header -->
              <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
                <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Partneri Juaj në Ekselencën e Sigurisë në Punë</p>
              </div>
              
              <!-- Content -->
              <div style="padding: 40px 30px;">
                <div style="text-align: center; margin-bottom: 30px;">
                  <table style="margin: 0 auto 20px auto;">
                    <tr>
                      <td style="background-color: #16a34a; color: white; width: 60px; height: 60px; border-radius: 30px; text-align: center; vertical-align: middle; font-size: 24px; font-weight: bold;">📧</td>
                    </tr>
                  </table>
                  <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Mirë se vini në Buletinin Tonë!</h2>
                  <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">Faleminderit që u bashkuat me komunitetin tonë të sigurisën në punë.</p>
                </div>
                
                <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
                  <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 Çfarë Do të Merrni</h3>
                  <ul style="margin: 0; padding-left: 20px; color: #0c4a6e; line-height: 1.8;">
                    <li>🛡️ Këshilla dhe praktika më të mira për sigurinë në punë</li>
                    <li>📋 Rregullore të reja sigurie dhe përditësime rreth ligjit të sigurisë në punë</li>
                    <li>🎓 Mundësi trajnimi rreth sigurisë në punë</li>
                    <li>📰 Lajme nga industria e sigurisë në punë</li>
                    <li>💡 Këshilla prefesionale nga profesionistët tanë të sigurisë në punë</li>
                  </ul>
                </div>
                
                <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
                  <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">🎯 Përkushtimi Ynë</h3>
                  <p style="margin: 0; color: #92400e; line-height: 1.6;">
                    Ne jemi të përkushtuar t'ju ndihmojmë të mbani një vend pune të sigurt dhe të shëndetshëm. Buletini ynë ofron informacione praktike sigurie që mund t'i zbatoni menjëherë.
                  </p>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                  <p style="color: #6b7280; margin: 0 0 20px 0; font-size: 16px;">Filloni të eksploroni burimet tona të sigurisë:</p>
                  <a href="https://safework-kosova.com/sq/services" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Shërbimet Tona</a>
                  <a href="https://safework-kosova.com/sq/blog" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Blogu i Sigurisë</a>
                </div>
              </div>
              
              <!-- Contact Info -->
              <div style="background-color: #1f2937; padding: 30px 20px; text-align: center;">
                <h3 style="color: #ffffff; margin: 0 0 20px 0; font-size: 20px;">Informacionet e Kontaktit</h3>
                <div style="display: inline-block; text-align: left;">
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📧 Email:</strong> 
                    <a href="mailto:<EMAIL>" style="color: #60a5fa; text-decoration: none;"><EMAIL></a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📞 Telefoni:</strong> 
                    <a href="tel:+38344819701" style="color: #60a5fa; text-decoration: none;">+383 44 819 701</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">🌐 Faqja Web:</strong> 
                    <a href="https://safework-kosova.com" style="color: #60a5fa; text-decoration: none;">www.safework-kosova.com</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📍 Adresa:</strong> Rruga Fatmir Ratkoceri, Ferizaj 70000, Kosovë
                  </p>
                </div>
              </div>
              
              <!-- Footer -->
              <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="margin: 0; color: #6b7280; font-size: 12px;">
                  Nëse nuk jeni abonuar në këtë buletin, ju lutemi injoroni këtë email.<br>
                  Mund të çabonoheni në çdo kohë duke na kontaktuar në <EMAIL>
                </p>
              </div>
            </div>
          </body>
          </html>
        `;
        responseMessage = 'Jeni abonuar me sukses! Kontrolloni email-in tuaj për konfirmim.';
    }

    // Send welcome email to subscriber
    const { error: welcomeError } = await resend.emails.send({
      from: 'SafeWork Kosova <<EMAIL>>',
      to: [safeEmail],
      subject: subject,
      html: htmlBody,
    });

    if (welcomeError) {
      console.error('⚠️ Failed to send welcome email (but subscription recorded):', welcomeError);
      // Don't fail the request if welcome email fails
    }

    console.log('✅ Newsletter subscription processed successfully');
    res.status(200).json({ 
      message: responseMessage
    });

  } catch (error) {
    console.error('💥 Unexpected error in subscription process:', error);
    
    // Make sure we always return valid JSON
    try {
      return res.status(500).json({ 
        message: 'Error processing subscription', 
        details: error.message 
      });
    } catch (jsonError) {
      // If JSON response fails, send plain text
      res.status(500).send('Internal server error');
    }
  }
}


