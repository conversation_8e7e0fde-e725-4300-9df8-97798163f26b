import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Award, ArrowRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Hero from '../components/Hero';
import ServiceCard from '../components/ServiceCard';
import TestimonialCarousel from '../components/TestimonialCarousel';
import { CORE_SERVICES, getServiceIcon, getServiceLink } from '../constants/services';
import { useSEO, generateStructuredData } from '../contexts/SEOContext';
import { generateLocalBusinessSchema } from '../utils/localBusinessSchema';


import CaseStudyCard from '../components/CaseStudyCard';
import CtaSection from '../components/CtaSection';
// Images are now served from public folder
const heroImage = '/images/Hero.jpg';
const injuryImage = '/images/Injury.jpg';
const ppeImage = '/images/PPE.jpg';
const auditImage = '/images/audit.jpg';
const certImage = '/images/certification.jpg';


const HomePage: React.FC = () => {
  const { t, i18n } = useTranslation(['home', 'common']);
  const currentLang = i18n.language;
  const { updateSEO } = useSEO();

  // Update SEO for homepage
  useEffect(() => {
    const localBusinessSchema = generateLocalBusinessSchema(currentLang);
    
    // Organization schema for better SEO and rich snippets
    const organizationSchema = generateStructuredData('Organization', {
      name: 'SafeWork Kosova',
      url: 'https://www.safework-kosova.com',
      logo: '/images/safework-kosova-logo.png',
      description: currentLang === 'sq'
        ? 'SafeWork Kosova ofron shërbime profesionale për siguri në punë, vlerësim rreziku, trajnime dhe konsulencë për bizneset në Kosovë.'
        : 'SafeWork Kosova provides professional workplace safety services, risk assessments, training and consulting for businesses in Kosovo.',
      address: {
        '@type': 'PostalAddress',
        streetAddress: 'Fatmir Ratkoceri Street',
        addressLocality: 'Ferizaj',
        postalCode: '70000',
        addressCountry: 'XK'
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+383-44-819-701',
        contactType: 'customer service',
  email: '<EMAIL>'
      },
      sameAs: [
        'https://linkedin.com/company/safework-kosova',
        'https://facebook.com/safeworkkosova'
      ]
    });

    // Combine both schemas for comprehensive SEO
    const combinedSchema = {
      '@context': 'https://schema.org',
      '@graph': [localBusinessSchema, organizationSchema]
    };

    updateSEO({
      title: currentLang === 'sq'
        ? 'SafeWork Kosova - Siguria në Punë dhe Shëndetësia Profesionale'
        : 'SafeWork Kosova - Workplace Safety and Occupational Health',
      description: currentLang === 'sq'
        ? 'SafeWork Kosova ofron shërbime profesionale për siguri në punë, vlerësim rreziku, trajnime dhe konsulencë për bizneset në Kosovë. Partneri juaj në ekselencën e sigurisë.'
        : 'SafeWork Kosova provides professional workplace safety services, risk assessments, training and consulting for businesses in Kosovo. Your partner in safety excellence.',
      keywords: currentLang === 'sq'
        ? ['siguria në punë', 'shëndetësia profesionale', 'vlerësim rreziku', 'trajnime siguria', 'konsulencë siguria', 'Kosovë', 'SafeWork']
        : ['workplace safety', 'occupational health', 'risk assessment', 'safety training', 'safety consulting', 'Kosovo', 'SafeWork'],
      type: 'website',
      structuredData: combinedSchema,
      openGraph: {
        title: currentLang === 'sq'
          ? 'SafeWork Kosova - Partneri Juaj në Ekselencën e Sigurisë'
          : 'SafeWork Kosova - Your Partner in Safety Excellence',
        description: currentLang === 'sq'
          ? 'Shërbime profesionale për siguri në punë, vlerësim rreziku dhe trajnime për bizneset në Kosovë.'
          : 'Professional workplace safety services, risk assessments and training for businesses in Kosovo.',
        type: 'website'
      }
    });
  }, [currentLang, updateSEO]);

  // Use shared services configuration
  const services = CORE_SERVICES.map(service => ({
    id: service.id,
    icon: getServiceIcon(service.id, 'w-10 h-10'),
    link: getServiceLink(service.id, currentLang)
  }));

  // Kosovo workplace safety data (2019-2025) - from official sources
  const fatalAccidentsByYear = [
    { year: '2019', count: 6, source: 'LI baseline' },
    { year: '2020', count: 3, source: 'pandemic year' },
    { year: '2021', count: 9, source: 'BIRN/ATRC' },
    { year: '2022', count: 6, source: 'Labour Inspectorate' },
    { year: '2023', count: 15, source: 'Labour Inspectorate' },
    { year: '2024', count: 17, source: 'OSH Association' },
    { year: '2025*', count: 10, source: 'Jan-Apr only', isPartial: true }
  ];



  const inspectorData = [
    { year: '2022', inspectors: 37, inspections: 6136 },
    { year: '2023', inspectors: 60, inspections: 18144 },
    { year: '2024', inspectors: 160, inspections: 54432 }
  ];

  const sectorRisks = [
    { sectorKey: 'stats_sector_construction', riskKey: 'stats_risk_critical', issuesKey: 'stats_sector_construction_desc' },
    { sectorKey: 'stats_sector_public', riskKey: 'stats_risk_high', issuesKey: 'stats_sector_public_desc' },
    { sectorKey: 'stats_sector_other', riskKey: 'stats_risk_unknown', issuesKey: 'stats_sector_other_desc' }
  ];

  const stats = [
    { value: '66', labelKey: 'stat_total_fatalities' },
    { value: '183%', labelKey: 'stat_fatality_surge' },
    { value: '1,655', labelKey: 'stat_total_injuries' },
    { value: '333%', labelKey: 'stat_inspector_increase' }
  ];

  const featuredCaseStudies = [
    { id: 'trepca-safety-improvement', image: injuryImage, link: `/${currentLang}/case-studies/trepca-safety-improvement` },
    { id: 'highway-construction-safety', image: ppeImage, link: `/${currentLang}/case-studies/highway-construction-safety` },
    { id: 'qkuk-risk-assessment', image: auditImage, link: `/${currentLang}/case-studies/qkuk-risk-assessment` }
  ];

  return (
    <div className="bg-white dark:bg-gray-900 transition-colors duration-300">
      <Hero
        title={t('hero_title')}
        subtitle={t('hero_subtitle')}
        ctaText={t('hero_cta')}
        ctaLink={`/${currentLang}/contact#formular-kontakti`}
        bgImage={heroImage}
      />

      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('services_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('services_subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service) => (<ServiceCard key={service.id} id={service.id} title={t(`service_${service.id}_title`)} description={t(`service_${service.id}_desc`)} icon={service.icon} link={service.link} />))}
          </div>
          <div className="text-center mt-12">
            <Link to={`/${currentLang}/services`} className="inline-flex items-center text-blue-800 dark:text-blue-300 font-medium hover:text-orange-500 transition-colors group">
              {t('services_all_link')}
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('stats_title')}</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">{t('stats_subtitle')}</p>
          </div>

          {/* Key Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-l-4 border-red-600">
                <div className="text-3xl md:text-4xl font-bold text-red-600 dark:text-red-400 mb-2">{stat.value}</div>
                <div className="text-gray-800 dark:text-gray-100 font-medium text-sm">{t(stat.labelKey)}</div>
              </div>
            ))}
          </div>

          {/* Visual Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Fatalities Trend Chart */}
            <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('stats_chart_fatalities')}</h3>
              <div className="h-64 relative">
                <div className="absolute inset-0 flex items-end justify-between px-2">
                  {fatalAccidentsByYear.map((item, i) => (
                    <div key={i} className="flex flex-col items-center" style={{ width: '12%' }}>
                      <div
                        className={`w-8 rounded-t transition-all duration-500 hover:opacity-80 ${item.isPartial ? 'bg-orange-500' : 'bg-red-600'
                          }`}
                        style={{
                          height: `${Math.max((item.count / 17) * 200, 8)}px`,
                          marginBottom: '8px'
                        }}
                        title={`${item.count} fatalities in ${item.year} (${item.source})`}
                      />
                      <div className="text-xs text-gray-600 dark:text-gray-300 font-medium mb-1 text-center">
                        {item.year}
                      </div>
                      <div className="text-sm font-bold text-gray-800 dark:text-gray-100">
                        {item.count}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="absolute bottom-0 left-0 right-0 h-px bg-gray-300 dark:bg-gray-600" />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                {t('stats_chart_note')}
              </p>
            </div>

            {/* Inspectors vs Inspections Chart */}
            <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('stats_chart_inspections')}</h3>
              <div className="h-72 relative mb-6">
                <div className="absolute inset-0 flex items-end justify-center pb-12">
                  {inspectorData.map((item, i) => (
                    <div key={i} className="flex flex-col items-center mx-6" style={{ width: '25%' }}>
                      {/* Bars container */}
                      <div className="flex items-end justify-center mb-3">
                        {/* Inspectors bar (blue) */}
                        <div className="flex flex-col items-center mr-2">
                          <div
                            className="w-8 bg-blue-600 rounded-t transition-all duration-300 hover:bg-blue-700"
                            style={{ height: `${Math.max((item.inspectors / 160) * 160, 12)}px` }}
                            title={`${item.inspectors} inspectors in ${item.year}`}
                          />
                          <div className="text-xs text-blue-600 font-bold mt-1">{item.inspectors}</div>
                        </div>
                        {/* Inspections bar (green) */}
                        <div className="flex flex-col items-center ml-2">
                          <div
                            className="w-8 bg-green-600 rounded-t transition-all duration-300 hover:bg-green-700"
                            style={{ height: `${Math.max((item.inspections / 54432) * 160, 12)}px` }}
                            title={`${item.inspections.toLocaleString()} inspections in ${item.year}`}
                          />
                          <div className="text-xs text-green-600 font-bold mt-1">{(item.inspections / 1000).toFixed(0)}K</div>
                        </div>
                      </div>
                      {/* Year label */}
                      <div className="text-sm text-gray-600 dark:text-gray-300 font-medium text-center">
                        {item.year}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-center space-x-6 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-600 rounded mr-2"></div>
                  <span className="text-gray-600 dark:text-gray-300">{t('stats_chart_legend_inspectors')}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-600 rounded mr-2"></div>
                  <span className="text-gray-600 dark:text-gray-300">{t('stats_chart_legend_inspections')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Sector Risk Analysis */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">{t('stats_sectors_title')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {sectorRisks.map((sector, index) => (
                <div key={index} className={`bg-white dark:bg-gray-700 p-6 rounded-lg shadow-lg border-l-4 ${sector.riskKey === 'stats_risk_critical' ? 'border-red-600' :
                  sector.riskKey === 'stats_risk_high' ? 'border-orange-500' : 'border-gray-400'
                  }`}>
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100">{t(sector.sectorKey)}</h4>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${sector.riskKey === 'stats_risk_critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      sector.riskKey === 'stats_risk_high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                      }`}>
                      {t(sector.riskKey)}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">{t(sector.issuesKey)}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Data Reliability Warning */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-8">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">{t('stats_warning_title')}</h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">{t('stats_warning_text')}</p>
              </div>
            </div>
          </div>

          {/* Sources and CTA */}
          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6 max-w-4xl mx-auto">{t('stats_sources')}</p>
            <div className="bg-gradient-to-r from-red-600 to-orange-600 text-white p-6 rounded-lg mb-8">
              <p className="text-lg font-medium mb-4">{t('stats_cta_message')}</p>
              <Link
                to={`/${currentLang}/contact`}
                className="inline-flex items-center bg-white text-red-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                {t('stats_cta_button')}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center md:space-x-12 lg:space-x-16">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <img src={certImage} alt={t('certs_title')} className="rounded-lg shadow-lg w-full object-cover aspect-video" />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-5">{t('certs_title')}</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">{t('certs_subtitle')}</p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('certs_item1_title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('certs_item1_subtitle')}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('certs_item2_title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('certs_item2_subtitle')}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Award className="w-6 h-6 text-orange-500 mt-1 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">{t('certs_item3_title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('certs_item3_subtitle')}</p>
                  </div>
                </div>
              </div>
              <Link to={`/${currentLang}/about`} className="inline-flex items-center text-blue-800 dark:text-blue-300 font-medium mt-8 hover:text-orange-500 transition-colors group">
                {t('certs_link')}
                <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('testimonials_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('testimonials_subtitle')}</p>
          </div>
          <TestimonialCarousel />
        </div>
      </section>

      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{t('caseStudies_title')}</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('caseStudies_subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCaseStudies.map((cs) => (<CaseStudyCard key={cs.id} id={cs.id} title={t(`caseStudy_${cs.id.split('-')[0]}_title`)} description={t(`caseStudy_${cs.id.split('-')[0]}_desc`)} image={cs.image} category={t(`caseStudy_${cs.id.split('-')[0]}_category`)} link={cs.link} />))}
          </div>
          <div className="text-center mt-12">
            <Link to={`/${currentLang}/case-studies`} className="inline-flex items-center text-blue-800 dark:text-blue-300 font-medium hover:text-orange-500 transition-colors group">
              {t('caseStudies_all_link')}
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* <section className="py-16 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-950">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-center text-2xl font-semibold text-blue-800 dark:text-blue-200 mb-10">{t('clients_title')}</h2>
          <div className="max-w-6xl mx-auto"><ClientLogos /></div>
        </div>
      </section> */}

      <CtaSection
        title={t('home_cta_title')}
        description={t('home_cta_subtitle')}
        ctaText={t('home_cta_button')}
        ctaLink={`/${currentLang}/contact#formular-kontakti`}
        className="dark:bg-gray-900 dark:text-gray-100"
      />
    </div>
  );
};

export default HomePage;