import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpBackend from 'i18next-http-backend';

// List all namespaces used in the project
const namespaces = [
  'common',
  'home',
  'about',
  'blog',
  'case-studies',
  'contact',
  'privacy',
  'services',
  'terms',
];


if (!i18n.isInitialized) {
  i18n
    .use(HttpBackend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      supportedLngs: ['sq', 'en'],
      fallbackLng: 'sq',
      ns: namespaces,
      defaultNS: 'common',
      fallbackNS: 'common',
      debug: false,
      detection: {
        order: ['path', 'localStorage', 'navigator'],
        caches: ['localStorage'],
      },
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
      },
      react: {
        useSuspense: true,
      },
      interpolation: {
        escapeValue: false,
      },
    });
}

export default i18n;