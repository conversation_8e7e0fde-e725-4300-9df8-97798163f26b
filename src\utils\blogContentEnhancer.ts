/**
 * Blog Content Enhancement Utilities
 * Provides functions to enhance blog content with callout boxes, process steps, and other formatting
 */

export interface CalloutConfig {
  type: 'info' | 'warning' | 'success' | 'tip';
  content: string;
}

export interface ProcessStep {
  title: string;
  description: string;
}

/**
 * Wraps content in a callout box
 */
export function createCallout(type: CalloutConfig['type'], content: string): string {
  return `<div class="callout-${type}"><p>${content}</p></div>`;
}

/**
 * Creates a process steps list
 */
export function createProcessSteps(steps: ProcessStep[]): string {
  const stepItems = steps.map(step => 
    `<li><strong>${step.title}</strong><br>${step.description}</li>`
  ).join('');
  
  return `<ol class="process-steps">${stepItems}</ol>`;
}

/**
 * Wraps content in a legal section
 */
export function createLegalSection(title: string, content: string): string {
  return `<div class="legal-section"><h3>${title}</h3><p>${content}</p></div>`;
}

/**
 * Highlights safety-related text
 */
export function highlightSafety(text: string): string {
  return `<span class="safety-highlight">${text}</span>`;
}

/**
 * Creates an enhanced quote block
 */
export function createQuote(quote: string, author?: string): string {
  const authorText = author ? `<cite>— ${author}</cite>` : '';
  return `<blockquote><p>${quote}</p>${authorText}</blockquote>`;
}

/**
 * Enhances existing blog content by automatically detecting and converting patterns
 */
export function enhanceBlogContent(content: string): string {
  let enhanced = content;

  // Convert [INFO] blocks to info callouts
  enhanced = enhanced.replace(
    /\[INFO\](.*?)\[\/INFO\]/gs,
    (match, content) => createCallout('info', content.trim())
  );

  // Convert [WARNING] blocks to warning callouts
  enhanced = enhanced.replace(
    /\[WARNING\](.*?)\[\/WARNING\]/gs,
    (match, content) => createCallout('warning', content.trim())
  );

  // Convert [SUCCESS] blocks to success callouts
  enhanced = enhanced.replace(
    /\[SUCCESS\](.*?)\[\/SUCCESS\]/gs,
    (match, content) => createCallout('success', content.trim())
  );

  // Convert [TIP] blocks to tip callouts
  enhanced = enhanced.replace(
    /\[TIP\](.*?)\[\/TIP\]/gs,
    (match, content) => createCallout('tip', content.trim())
  );

  // Convert [LEGAL] blocks to legal sections
  enhanced = enhanced.replace(
    /\[LEGAL title="([^"]*)"](.*?)\[\/LEGAL\]/gs,
    (match, title, content) => createLegalSection(title, content.trim())
  );

  // Highlight safety keywords
  const safetyKeywords = [
    'sigurinë në punë',
    'workplace safety',
    'risk assessment',
    'vlerësim rreziku',
    'personal protective equipment',
    'pajisje mbrojtëse personale',
    'PPE',
    'health and safety',
    'shëndet dhe siguri'
  ];

  safetyKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
    enhanced = enhanced.replace(regex, (match) => highlightSafety(match));
  });

  return enhanced;
}

/**
 * Estimates reading time based on content length
 */
export function estimateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

/**
 * Extracts headings from content for table of contents
 */
export function extractHeadings(content: string): Array<{level: number, text: string, id: string}> {
  const headingRegex = /<h([2-4])[^>]*>(.*?)<\/h[2-4]>/gi;
  const headings: Array<{level: number, text: string, id: string}> = [];
  let match;
  let index = 0;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = parseInt(match[1]);
    const text = match[2].replace(/<[^>]*>/g, '').trim();
    const id = `heading-${index}`;
    
    headings.push({ level, text, id });
    index++;
  }

  return headings;
}

/**
 * Adds IDs to headings in content for navigation
 */
export function addHeadingIds(content: string): string {
  let index = 0;
  return content.replace(/<h([2-4])([^>]*)>(.*?)<\/h[2-4]>/gi, (match, level, attrs, text) => {
    const id = `heading-${index}`;
    index++;
    return `<h${level}${attrs} id="${id}">${text}</h${level}>`;
  });
}
