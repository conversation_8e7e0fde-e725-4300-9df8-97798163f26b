// API Client for SafeWork Kosova
import type { ApiResponse, ContactFormData, NewsletterSubscription } from '../types';

// Base API configuration
const API_BASE_URL = '/api';

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || `HTTP ${response.status}: ${response.statusText}`,
        details: data.details,
      };
    }

    return {
      success: true,
      data,
      message: data.message,
    };
  } catch (error) {
    console.error('API Request failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error occurred',
    };
  }
}

// Contact API
export const contactApi = {
  /**
   * Submit contact form
   */
  submit: async (formData: ContactFormData): Promise<ApiResponse> => {
    return apiRequest('/contact', {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  },

  /**
   * Test email functionality (development only)
   */
  testEmail: async (testKey: string): Promise<ApiResponse> => {
    return apiRequest('/test-email', {
      method: 'POST',
      headers: {
        'X-Test-Key': testKey,
      },
    });
  },
};

// Newsletter API
export const newsletterApi = {
  /**
   * Subscribe to newsletter
   */
  subscribe: async (subscription: NewsletterSubscription): Promise<ApiResponse> => {
    return apiRequest('/subscribe', {
      method: 'POST',
      body: JSON.stringify(subscription),
    });
  },
};

// Combined API client
export const apiClient = {
  contact: contactApi,
  newsletter: newsletterApi,
};

// Utility functions for API handling
export const apiUtils = {
  /**
   * Check if API response indicates success
   */
  isSuccess: (response: ApiResponse): boolean => {
    return response.success === true;
  },

  /**
   * Extract error message from API response
   */
  getErrorMessage: (response: ApiResponse): string => {
    return response.error || response.message || 'An unknown error occurred';
  },

  /**
   * Extract detailed error information
   */
  getErrorDetails: (response: ApiResponse): string[] => {
    return response.details || [];
  },

  /**
   * Create a standardized error response
   */
  createErrorResponse: (error: string, details?: string[]): ApiResponse => {
    return {
      success: false,
      error,
      details,
    };
  },

  /**
   * Create a standardized success response
   */
  createSuccessResponse: <T>(data?: T, message?: string): ApiResponse<T> => {
    return {
      success: true,
      data,
      message,
    };
  },
};

export default apiClient;
