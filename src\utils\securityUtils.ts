import DOMPurify from 'dompurify';

/**
 * Security utility functions for safe HTML rendering and input sanitization
 */

// Configuration for DOMPurify to allow safe blog content
const BLOG_CONTENT_CONFIG = {
  ALLOWED_TAGS: [
    'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'code', 'pre',
    'table', 'thead', 'tbody', 'tr', 'th', 'td', 'span', 'div'
  ],
  ALLOWED_ATTR: [
    'href', 'src', 'alt', 'title', 'class', 'id', 'target', 'rel'
  ],
  ALLOW_DATA_ATTR: false,
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button', 'iframe'],
  FORBID_ATTR: [
    'onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur',
    'onchange', 'onsubmit', 'onreset', 'onselect', 'onkeydown', 'onkeyup',
    'onkeypress', 'onmousedown', 'onmouseup', 'onmousemove', 'onmouseout'
  ],
  // Ensure links open safely
  ADD_ATTR: { 'target': '_blank', 'rel': 'noopener noreferrer' },
  // Remove any data attributes that could be used for XSS
  SANITIZE_DOM: true,
  KEEP_CONTENT: true,
};

// Stricter configuration for user input (comments, forms, etc.)
const USER_INPUT_CONFIG = {
  ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u'],
  ALLOWED_ATTR: [],
  ALLOW_DATA_ATTR: false,
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button', 'iframe', 'a', 'img'],
  FORBID_ATTR: ['onclick', 'onload', 'onerror', 'style'],
  SANITIZE_DOM: true,
  KEEP_CONTENT: true,
};

/**
 * Sanitizes HTML content for safe rendering in blog posts
 * @param html - The HTML string to sanitize
 * @returns Sanitized HTML string safe for rendering
 */
export const sanitizeBlogContent = (html: string): string => {
  if (!html || typeof html !== 'string') return '';
  
  try {
    return DOMPurify.sanitize(html, BLOG_CONTENT_CONFIG);
  } catch (error) {
    console.error('Error sanitizing blog content:', error);
    return '';
  }
};

/**
 * Sanitizes user input for safe rendering (stricter than blog content)
 * @param input - The user input string to sanitize
 * @returns Sanitized string safe for rendering
 */
export const sanitizeUserInput = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  try {
    return DOMPurify.sanitize(input, USER_INPUT_CONFIG);
  } catch (error) {
    console.error('Error sanitizing user input:', error);
    return '';
  }
};

/**
 * Strips all HTML tags and returns plain text
 * @param html - The HTML string to strip
 * @returns Plain text without HTML tags
 */
export const stripHtmlTags = (html: string): string => {
  if (!html || typeof html !== 'string') return '';
  
  try {
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [], KEEP_CONTENT: true });
  } catch (error) {
    console.error('Error stripping HTML tags:', error);
    return '';
  }
};

/**
 * Validates and sanitizes email content for safe rendering
 * @param content - The email content to sanitize
 * @returns Sanitized email content
 */
export const sanitizeEmailContent = (content: string): string => {
  if (!content || typeof content !== 'string') return '';
  
  // Email content should be very restrictive
  const emailConfig = {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em'],
    ALLOWED_ATTR: [],
    ALLOW_DATA_ATTR: false,
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button', 'iframe', 'a', 'img'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'style'],
    SANITIZE_DOM: true,
    KEEP_CONTENT: true,
  };
  
  try {
    return DOMPurify.sanitize(content, emailConfig);
  } catch (error) {
    console.error('Error sanitizing email content:', error);
    return '';
  }
};

/**
 * Validates that a string is safe for use in URLs
 * @param input - The input to validate
 * @returns Boolean indicating if the input is safe for URLs
 */
export const isValidUrlParameter = (input: string): boolean => {
  if (!input || typeof input !== 'string') return false;
  
  // Allow only alphanumeric characters, hyphens, and underscores
  const urlSafeRegex = /^[a-zA-Z0-9\-_]+$/;
  return urlSafeRegex.test(input) && input.length <= 100;
};

// Common email domains for reference (currently unused but kept for future features)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const COMMON_EMAIL_DOMAINS = [
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
  'icloud.com', 'live.com', 'msn.com', 'ymail.com', 'protonmail.com',
  'zoho.com', 'mail.com', 'gmx.com', 'fastmail.com'
];

const DOMAIN_TYPOS = {
  'gmai.com': 'gmail.com',
  'gmial.com': 'gmail.com',
  'gmaill.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'yahoo.co': 'yahoo.com',
  'hotmai.com': 'hotmail.com',
  'hotmial.com': 'hotmail.com',
  'hotml.com': 'hotmail.com',
  'hotmil.com': 'hotmail.com',
  'hotmail.co': 'hotmail.com',
  'outlok.com': 'outlook.com',
  'outlook.co': 'outlook.com',
  'outloo.com': 'outlook.com'
};

/**
 * Validates email format with enhanced domain checking
 * @param email - The email to validate
 * @returns Boolean indicating if the email format is valid
 */
export const isValidEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') return false;

  // More robust email validation regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // Additional checks
  if (email.length > 254) return false; // RFC 5321 limit
  if (email.includes('..')) return false; // No consecutive dots
  if (email.startsWith('.') || email.endsWith('.')) return false; // No leading/trailing dots
  if (email.includes('@.') || email.includes('.@')) return false; // No dots adjacent to @

  // Basic regex validation
  if (!emailRegex.test(email)) return false;

  // Extract domain for additional validation
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;

  // Check for common typos
  if (DOMAIN_TYPOS[domain]) {
    return false; // Reject known typos
  }

  // Require minimum domain length (helps catch obvious typos)
  if (domain.length < 4) return false; // e.g., "a.b" is too short

  // Require at least one dot in domain
  if (!domain.includes('.')) return false;

  // Check TLD length (most TLDs are 2-6 characters)
  const tld = domain.split('.').pop();
  if (!tld || tld.length < 2 || tld.length > 6) return false;

  return true;
};

/**
 * Suggests correction for common email typos
 * @param email - The email to check for typos
 * @returns Suggested correction or null if no suggestion
 */
export const suggestEmailCorrection = (email: string): string | null => {
  if (!email || typeof email !== 'string') return null;

  const parts = email.split('@');
  if (parts.length !== 2) return null;

  const [localPart, domain] = parts;
  const lowerDomain = domain.toLowerCase();

  if (DOMAIN_TYPOS[lowerDomain]) {
    return `${localPart}@${DOMAIN_TYPOS[lowerDomain]}`;
  }

  return null;
};

/**
 * Validates phone number format (basic validation)
 * @param phone - The phone number to validate
 * @returns Boolean indicating if the phone format is valid
 */
export const isValidPhone = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') return false;

  // Allow digits, spaces, hyphens, parentheses, and plus sign
  const phoneRegex = /^[\d\s\-()]+$/;
  return phoneRegex.test(phone) && phone.length >= 7 && phone.length <= 20;
};

/**
 * Escapes special characters in a string for safe use in regular expressions
 * @param string - The string to escape
 * @returns Escaped string safe for regex use
 */
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};
