// Health check endpoint for Vercel deployment
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: 'vercel',
    services: {
      api: 'operational',
      email: process.env.RESEND_API_KEY ? 'configured' : 'not_configured'
    }
  };

  res.status(200).json(healthData);
}
