@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Blog Content Styling */
.blog-content-enhanced {
  /* Base typography improvements */
  line-height: 1.75;
  color: rgb(var(--color-text-secondary));
}

.blog-content-enhanced h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: rgb(var(--color-text-primary));
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 3px solid rgb(var(--color-secondary-200));
  position: relative;
}

.blog-content-enhanced h2::before {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgb(var(--color-primary-500)), rgb(var(--color-secondary-500)));
  border-radius: 2px;
}

.blog-content-enhanced h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: rgb(var(--color-text-primary));
  margin-top: 2rem;
  margin-bottom: 1rem;
  padding-left: 1rem;
  border-left: 4px solid rgb(var(--color-primary-500));
}

.blog-content-enhanced h4 {
  font-size: 1.25rem;
  font-weight: 500;
  color: rgb(var(--color-text-primary));
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.blog-content-enhanced p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: rgb(var(--color-text-secondary));
}

.blog-content-enhanced p:first-of-type {
  font-size: 1.125rem;
  font-weight: 400;
  color: rgb(var(--color-text-primary));
}

/* Enhanced list styling */
.blog-content-enhanced ul {
  margin: 1.5rem 0;
  padding-left: 0;
  list-style: none;
}

.blog-content-enhanced ul li {
  position: relative;
  margin-bottom: 0.75rem;
  padding-left: 2rem;
  line-height: 1.7;
  color: rgb(var(--color-text-secondary));
}

.blog-content-enhanced ul li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.6rem;
  width: 8px;
  height: 8px;
  background: rgb(var(--color-primary-500));
  border-radius: 50%;
}

.blog-content-enhanced ul li strong {
  color: rgb(var(--color-text-primary));
  font-weight: 600;
}

/* Nested lists */
.blog-content-enhanced ul ul {
  margin: 0.5rem 0;
  padding-left: 1rem;
}

.blog-content-enhanced ul ul li::before {
  background: rgb(var(--color-secondary-500));
  width: 6px;
  height: 6px;
  top: 0.65rem;
}

/* Ordered lists */
.blog-content-enhanced ol {
  margin: 1.5rem 0;
  padding-left: 0;
  counter-reset: list-counter;
}

.blog-content-enhanced ol li {
  position: relative;
  margin-bottom: 0.75rem;
  padding-left: 2.5rem;
  line-height: 1.7;
  color: rgb(var(--color-text-secondary));
  counter-increment: list-counter;
}

.blog-content-enhanced ol li::before {
  content: counter(list-counter);
  position: absolute;
  left: 0;
  top: 0;
  width: 1.5rem;
  height: 1.5rem;
  background: rgb(var(--color-primary-500));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Callout boxes */
.callout-info {
  background: rgb(var(--color-info-bg));
  border: 1px solid rgb(var(--color-info) / 0.3);
  border-left: 4px solid rgb(var(--color-info));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  position: relative;
}

.callout-info::before {
  content: 'ℹ️';
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: 1.25rem;
}

.callout-info p {
  margin-left: 2rem;
  margin-bottom: 0;
  color: rgb(var(--color-text-primary));
}

.callout-warning {
  background: rgb(var(--color-warning-bg));
  border: 1px solid rgb(var(--color-warning) / 0.3);
  border-left: 4px solid rgb(var(--color-warning));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  position: relative;
}

.callout-warning::before {
  content: '⚠️';
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: 1.25rem;
}

.callout-warning p {
  margin-left: 2rem;
  margin-bottom: 0;
  color: rgb(var(--color-text-primary));
}

.callout-success {
  background: rgb(var(--color-success-bg));
  border: 1px solid rgb(var(--color-success) / 0.3);
  border-left: 4px solid rgb(var(--color-success));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  position: relative;
}

.callout-success::before {
  content: '✅';
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: 1.25rem;
}

.callout-success p {
  margin-left: 2rem;
  margin-bottom: 0;
  color: rgb(var(--color-text-primary));
}

.callout-tip {
  background: rgb(var(--color-secondary-50));
  border: 1px solid rgb(var(--color-secondary-200));
  border-left: 4px solid rgb(var(--color-secondary-500));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  position: relative;
}

.callout-tip::before {
  content: '💡';
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: 1.25rem;
}

.callout-tip p {
  margin-left: 2rem;
  margin-bottom: 0;
  color: rgb(var(--color-text-primary));
}

/* Legal content styling */
.legal-section {
  background: rgb(var(--color-neutral-50));
  border: 1px solid rgb(var(--color-neutral-200));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.legal-section h3 {
  color: rgb(var(--color-text-primary));
  font-weight: 600;
  margin-bottom: 1rem;
  border-left: none;
  padding-left: 0;
}

/* Safety highlighting */
.safety-highlight {
  background: linear-gradient(120deg, rgb(var(--color-warning-bg)) 0%, rgb(var(--color-warning-bg)) 100%);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  color: rgb(var(--color-text-primary));
}

/* Process steps */
.process-steps {
  counter-reset: step-counter;
}

.process-steps > li {
  counter-increment: step-counter;
  background: rgb(var(--color-surface));
  border: 1px solid rgb(var(--color-border));
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.process-steps > li::before {
  content: counter(step-counter);
  position: absolute;
  left: -0.75rem;
  top: 1rem;
  width: 2rem;
  height: 2rem;
  background: rgb(var(--color-primary-500));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  border: 3px solid rgb(var(--color-background));
}

/* Quote styling */
.blog-content-enhanced blockquote {
  position: relative;
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background: rgb(var(--color-surface-variant));
  border-left: 4px solid rgb(var(--color-primary-500));
  border-radius: 0.5rem;
  font-style: italic;
  font-size: 1.125rem;
  line-height: 1.6;
}

.blog-content-enhanced blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: rgb(var(--color-primary-500));
  font-family: serif;
}

.blog-content-enhanced blockquote p {
  margin-bottom: 0;
  color: rgb(var(--color-text-primary));
}

/* Code styling */
.blog-content-enhanced code {
  background: rgb(var(--color-neutral-100));
  color: rgb(var(--color-text-primary));
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.blog-content-enhanced pre {
  background: rgb(var(--color-neutral-900));
  color: rgb(var(--color-neutral-100));
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.blog-content-enhanced pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

/* Links */
.blog-content-enhanced a {
  color: rgb(var(--color-secondary-500));
  text-decoration: underline;
  text-decoration-color: rgb(var(--color-secondary-300));
  text-underline-offset: 0.25rem;
  transition: all 0.2s ease;
}

.blog-content-enhanced a:hover {
  color: rgb(var(--color-primary-500));
  text-decoration-color: rgb(var(--color-primary-500));
}

/* Strong text */
.blog-content-enhanced strong {
  color: rgb(var(--color-text-primary));
  font-weight: 600;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .blog-content-enhanced h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .blog-content-enhanced h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .blog-content-enhanced p:first-of-type {
    font-size: 1rem;
  }

  .callout-info,
  .callout-warning,
  .callout-success,
  .callout-tip {
    padding: 1rem;
    margin: 1rem 0;
  }

  .callout-info p,
  .callout-warning p,
  .callout-success p,
  .callout-tip p {
    margin-left: 1.5rem;
  }

  .process-steps > li {
    padding: 1rem;
    margin-left: 1rem;
  }

  .process-steps > li::before {
    left: -0.5rem;
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }
}

/* Print styles */
@media print {
  .blog-content-enhanced {
    color: black !important;
  }

  .blog-content-enhanced h2,
  .blog-content-enhanced h3,
  .blog-content-enhanced h4 {
    color: black !important;
    break-after: avoid;
  }

  .blog-content-enhanced p {
    color: black !important;
  }

  .callout-info,
  .callout-warning,
  .callout-success,
  .callout-tip {
    border: 1px solid #ccc !important;
    background: #f9f9f9 !important;
    break-inside: avoid;
  }

  .process-steps > li {
    break-inside: avoid;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.blog-content-enhanced a:focus {
  outline: 2px solid rgb(var(--color-secondary-500));
  outline-offset: 2px;
  border-radius: 2px;
}

/* Selection styles */
.blog-content-enhanced ::selection {
  background: rgb(var(--color-primary-200));
  color: rgb(var(--color-text-primary));
}

/* CSS Custom Properties for Theme System */
:root {
  /* Light theme colors - Primary palette (Orange) - WCAG AA Compliant */
  --color-primary-50: 255 247 237;
  --color-primary-100: 255 237 213;
  --color-primary-200: 254 215 170;
  --color-primary-300: 253 186 116;
  --color-primary-400: 251 146 60;
  --color-primary-500: 194 65 12; /* Updated for better contrast - was 249 115 22 */
  --color-primary-600: 194 65 12;
  --color-primary-700: 194 65 12;
  --color-primary-800: 154 52 18;
  --color-primary-900: 124 45 18;
  --color-primary-950: 67 20 7;

  /* Light theme colors - Secondary palette (Blue) - WCAG AA Compliant */
  --color-secondary-50: 239 246 255;
  --color-secondary-100: 219 234 254;
  --color-secondary-200: 191 219 254;
  --color-secondary-300: 147 197 253;
  --color-secondary-400: 96 165 250;
  --color-secondary-500: 29 78 216; /* Updated for better contrast - was 59 130 246 */
  --color-secondary-600: 37 99 235;
  --color-secondary-700: 29 78 216;
  --color-secondary-800: 30 64 175;
  --color-secondary-900: 30 58 138;
  --color-secondary-950: 23 37 84;

  /* Light theme colors - Neutral palette (Gray) */
  --color-neutral-50: 249 250 251;
  --color-neutral-100: 243 244 246;
  --color-neutral-200: 229 231 235;
  --color-neutral-300: 209 213 219;
  --color-neutral-400: 156 163 175;
  --color-neutral-500: 107 114 128;
  --color-neutral-600: 75 85 99;
  --color-neutral-700: 55 65 81;
  --color-neutral-800: 31 41 55;
  --color-neutral-900: 17 24 39;
  --color-neutral-950: 3 7 18;

  /* Light theme semantic colors - WCAG AA Compliant */
  --color-background: 255 255 255;
  --color-surface: 249 250 251;
  --color-surface-variant: 243 244 246;
  --color-surface-hover: 229 231 235;
  --color-border: 229 231 235;
  --color-border-hover: 209 213 219;
  --color-accent: 5 150 105; /* emerald-600 - Better contrast (4.8:1) - was 16 185 129 */
  --color-accent-hover: 4 120 87; /* emerald-700 - Even better contrast (6.1:1) - was 5 150 105 */

  /* Light theme text colors - WCAG AA Compliant */
  --color-text-primary: 17 24 39; /* gray-900 - Excellent contrast (16.7:1) */
  --color-text-secondary: 55 65 81; /* gray-700 - Good contrast (7.6:1) - was 75 85 99 */
  --color-text-muted: 107 114 128; /* gray-500 - Meets AA for large text (4.6:1) - was 156 163 175 */
  --color-text-inverse: 255 255 255;

  /* Light theme status colors */
  --color-success: 34 197 94;
  --color-success-bg: 240 253 244;
  --color-warning: 245 158 11;
  --color-warning-bg: 255 251 235;
  --color-error: 239 68 68;
  --color-error-bg: 254 242 242;
  --color-info: 59 130 246;
  --color-info-bg: 239 246 255;

  /* Spacing and sizing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-theme: 200ms ease-in-out;
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;

  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark theme colors */
.dark {
  /* Dark theme colors - Primary palette (Orange) - WCAG AA Compliant */
  --color-primary-50: 67 20 7;
  --color-primary-100: 124 45 18;
  --color-primary-200: 154 52 18;
  --color-primary-300: 194 65 12;
  --color-primary-400: 234 88 12;
  --color-primary-500: 251 146 60; /* orange-400 - Better contrast on dark (8.2:1) - was 249 115 22 */
  --color-primary-600: 251 146 60;
  --color-primary-700: 253 186 116;
  --color-primary-800: 254 215 170;
  --color-primary-900: 255 237 213;
  --color-primary-950: 255 247 237;

  /* Dark theme colors - Secondary palette (Blue) - WCAG AA Compliant */
  --color-secondary-50: 23 37 84;
  --color-secondary-100: 30 58 138;
  --color-secondary-200: 30 64 175;
  --color-secondary-300: 29 78 216;
  --color-secondary-400: 37 99 235;
  --color-secondary-500: 96 165 250; /* blue-400 - Better contrast on dark (9.1:1) - was 59 130 246 */
  --color-secondary-600: 96 165 250;
  --color-secondary-700: 147 197 253;
  --color-secondary-800: 191 219 254;
  --color-secondary-900: 219 234 254;
  --color-secondary-950: 239 246 255;

  /* Dark theme colors - Neutral palette (Gray - inverted) */
  --color-neutral-50: 3 7 18;
  --color-neutral-100: 17 24 39;
  --color-neutral-200: 31 41 55;
  --color-neutral-300: 55 65 81;
  --color-neutral-400: 75 85 99;
  --color-neutral-500: 107 114 128;
  --color-neutral-600: 156 163 175;
  --color-neutral-700: 209 213 219;
  --color-neutral-800: 229 231 235;
  --color-neutral-900: 243 244 246;
  --color-neutral-950: 249 250 251;

  /* Dark theme semantic colors */
  --color-background: 17 24 39;
  --color-surface: 31 41 55;
  --color-surface-variant: 55 65 81;
  --color-surface-hover: 75 85 99;
  --color-border: 75 85 99;
  --color-border-hover: 107 114 128;
  --color-accent: 52 211 153;
  --color-accent-hover: 16 185 129;

  /* Dark theme text colors */
  --color-text-primary: 249 250 251;
  --color-text-secondary: 209 213 219;
  --color-text-muted: 156 163 175;
  --color-text-inverse: 17 24 39;

  /* Dark theme status colors */
  --color-success: 52 211 153;
  --color-success-bg: 6 78 59;
  --color-warning: 251 191 36;
  --color-warning-bg: 120 53 15;
  --color-error: 248 113 113;
  --color-error-bg: 127 29 29;
  --color-info: 96 165 250;
  --color-info-bg: 30 58 138;

  /* Dark theme shadows (adjusted for dark backgrounds) */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Fallbacks for browsers that don't support CSS custom properties */
@supports not (color: rgb(var(--color-primary-500))) {
  :root {
    /* Light theme fallbacks */
    --color-primary: #f97316;
    --color-secondary: #3b82f6;
    --color-background: #ffffff;
    --color-surface: #f9fafb;
    --color-text-primary: #111827;
    --color-text-secondary: #4b5563;
    --color-border: #e5e7eb;
    --color-accent: #10b981;
  }

  .dark {
    /* Dark theme fallbacks */
    --color-primary: #f97316;
    --color-secondary: #3b82f6;
    --color-background: #111827;
    --color-surface: #1f2937;
    --color-text-primary: #f9fafb;
    --color-text-secondary: #d1d5db;
    --color-border: #4b5563;
    --color-accent: #34d399;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans text-gray-800 antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-sans leading-tight;
  }
}

@layer components {
  /* WCAG AA Compliant Button Styles */
  .btn-primary {
    @apply inline-flex items-center justify-center px-5 py-2.5 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer;
    background-color: rgb(var(--color-primary-500));
    color: rgb(var(--color-text-inverse));
  }
  
  .btn-primary:hover {
    background-color: rgb(var(--color-primary-600));
  }
  
  .btn-primary:focus {
    ring-color: rgb(var(--color-primary-500));
  }

  .btn-primary-lg {
    @apply inline-flex items-center justify-center px-6 py-3 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    background-color: rgb(var(--color-primary-500));
    color: rgb(var(--color-text-inverse));
  }
  
  .btn-primary-lg:hover {
    background-color: rgb(var(--color-primary-600));
  }
  
  .btn-primary-lg:focus {
    ring-color: rgb(var(--color-primary-500));
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-5 py-2.5 bg-transparent font-medium border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    color: rgb(var(--color-secondary-500));
    border-color: rgb(var(--color-secondary-500));
  }
  
  .btn-secondary:hover {
    background-color: rgb(var(--color-secondary-50));
    border-color: rgb(var(--color-secondary-600));
  }
  
  .btn-secondary:focus {
    ring-color: rgb(var(--color-secondary-500));
  }
  
  .dark .btn-secondary:hover {
    background-color: rgb(var(--color-secondary-900));
  }

  .btn-secondary-lg {
    @apply inline-flex items-center justify-center px-6 py-3 font-medium border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    background-color: rgb(var(--color-background));
    color: rgb(var(--color-secondary-500));
    border-color: rgb(var(--color-background));
  }
  
  .btn-secondary-lg:hover {
    background-color: rgb(var(--color-secondary-50));
    border-color: rgb(var(--color-secondary-50));
  }
  
  .btn-secondary-lg:focus {
    ring-color: rgb(var(--color-secondary-500));
  }
  
  .dark .btn-secondary-lg:hover {
    background-color: rgb(var(--color-secondary-900));
    border-color: rgb(var(--color-secondary-900));
  }

  /* Accessible focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-offset-2;
    ring-color: rgb(var(--color-primary-500));
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .btn-primary, .btn-primary-lg {
      border: 2px solid transparent;
    }
    
    .btn-secondary, .btn-secondary-lg {
      border-width: 2px;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .btn-primary, .btn-primary-lg, .btn-secondary, .btn-secondary-lg {
      transition: none;
    }
  }

  .container {
    @apply max-w-7xl mx-auto;
  }
  
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  /* Skip link for keyboard navigation */
  .skip-link {
    @apply absolute left-4 z-50 px-4 py-2 rounded-md font-medium transition-transform;
    background-color: rgb(var(--color-primary-500));
    color: rgb(var(--color-text-inverse));
    top: -100px; /* Move completely off-screen */
    transform: translateY(0);
  }
  
  .skip-link:focus {
    top: 1rem; /* Position properly when focused */
    transform: translateY(0);
  }
}