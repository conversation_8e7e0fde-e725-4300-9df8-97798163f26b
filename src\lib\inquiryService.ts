import { getDatabase, generateId, inquiryToRow, rowToInquiry } from './database.js';
import type { Inquiry, InquiryRow } from '../types/database.js';

export interface CreateInquiryData {
  contactName: string;
  contactEmail: string;
  companyName?: string;
  inquiryCategory: Inquiry['inquiryCategory'];
  inquirySubcategory?: string;
  urgencyLevel: Inquiry['urgencyLevel'];
  description: string;
  requiresProfessional?: boolean;
}

export interface InquiryFilters {
  status?: Inquiry['status'];
  category?: Inquiry['inquiryCategory'];
  urgencyLevel?: Inquiry['urgencyLevel'];
  assignedTo?: string;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}

export interface UpdateInquiryData {
  status?: Inquiry['status'];
  assignedTo?: string;
  requiresProfessional?: boolean;
}

/**
 * Create a new inquiry
 */
export function createInquiry(data: CreateInquiryData): Inquiry {
  const db = getDatabase();
  
  const inquiry: Inquiry = {
    id: generateId(),
    contactName: data.contactName.trim(),
    contactEmail: data.contactEmail.trim().toLowerCase(),
    companyName: data.companyName?.trim(),
    inquiryCategory: data.inquiryCategory,
    inquirySubcategory: data.inquirySubcategory?.trim(),
    urgencyLevel: data.urgencyLevel,
    description: data.description.trim(),
    status: 'new',
    requiresProfessional: data.requiresProfessional || false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const row = inquiryToRow(inquiry);
  
  const stmt = db.prepare(`
    INSERT INTO inquiries (
      id, contact_name, contact_email, company_name, inquiry_category,
      inquiry_subcategory, urgency_level, description, status, assigned_to,
      requires_professional, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
  `);
  
  stmt.run(
    row.id,
    row.contact_name,
    row.contact_email,
    row.company_name,
    row.inquiry_category,
    row.inquiry_subcategory,
    row.urgency_level,
    row.description,
    row.status,
    row.assigned_to,
    row.requires_professional
  );

  return inquiry;
}

/**
 * Get inquiry by ID
 */
export function getInquiryById(id: string): Inquiry | null {
  const db = getDatabase();
  
  const stmt = db.prepare('SELECT * FROM inquiries WHERE id = ?');
  const row = stmt.get(id) as InquiryRow | undefined;
  
  return row ? rowToInquiry(row) : null;
}

/**
 * Get inquiries with filters
 */
export function getInquiries(filters: InquiryFilters = {}): Inquiry[] {
  const db = getDatabase();
  
  let query = 'SELECT * FROM inquiries WHERE 1=1';
  const params: any[] = [];
  
  if (filters.status) {
    query += ' AND status = ?';
    params.push(filters.status);
  }
  
  if (filters.category) {
    query += ' AND inquiry_category = ?';
    params.push(filters.category);
  }
  
  if (filters.urgencyLevel) {
    query += ' AND urgency_level = ?';
    params.push(filters.urgencyLevel);
  }
  
  if (filters.assignedTo) {
    query += ' AND assigned_to = ?';
    params.push(filters.assignedTo);
  }
  
  if (filters.dateFrom) {
    query += ' AND created_at >= ?';
    params.push(filters.dateFrom.toISOString());
  }
  
  if (filters.dateTo) {
    query += ' AND created_at <= ?';
    params.push(filters.dateTo.toISOString());
  }
  
  query += ' ORDER BY created_at DESC';
  
  if (filters.limit) {
    query += ' LIMIT ?';
    params.push(filters.limit);
    
    if (filters.offset) {
      query += ' OFFSET ?';
      params.push(filters.offset);
    }
  }
  
  const stmt = db.prepare(query);
  const rows = stmt.all(...params) as InquiryRow[];
  
  return rows.map(rowToInquiry);
}

/**
 * Update inquiry
 */
export function updateInquiry(id: string, data: UpdateInquiryData): Inquiry | null {
  const db = getDatabase();
  
  const updates: string[] = [];
  const params: any[] = [];
  
  if (data.status !== undefined) {
    updates.push('status = ?');
    params.push(data.status);
  }
  
  if (data.assignedTo !== undefined) {
    updates.push('assigned_to = ?');
    params.push(data.assignedTo);
  }
  
  if (data.requiresProfessional !== undefined) {
    updates.push('requires_professional = ?');
    params.push(data.requiresProfessional ? 1 : 0);
  }
  
  if (updates.length === 0) {
    return getInquiryById(id);
  }
  
  updates.push('updated_at = datetime(\'now\')');
  params.push(id);
  
  const query = `UPDATE inquiries SET ${updates.join(', ')} WHERE id = ?`;
  const stmt = db.prepare(query);
  const result = stmt.run(...params);
  
  if (result.changes === 0) {
    return null;
  }
  
  return getInquiryById(id);
}

/**
 * Delete inquiry
 */
export function deleteInquiry(id: string): boolean {
  const db = getDatabase();
  
  const stmt = db.prepare('DELETE FROM inquiries WHERE id = ?');
  const result = stmt.run(id);
  
  return result.changes > 0;
}

/**
 * Get inquiry statistics
 */
export function getInquiryStats(): {
  total: number;
  byStatus: Record<string, number>;
  byCategory: Record<string, number>;
  byUrgency: Record<string, number>;
} {
  const db = getDatabase();
  
  // Total count
  const totalStmt = db.prepare('SELECT COUNT(*) as count FROM inquiries');
  const total = (totalStmt.get() as { count: number }).count;
  
  // By status
  const statusStmt = db.prepare('SELECT status, COUNT(*) as count FROM inquiries GROUP BY status');
  const statusRows = statusStmt.all() as { status: string; count: number }[];
  const byStatus = statusRows.reduce((acc, row) => {
    acc[row.status] = row.count;
    return acc;
  }, {} as Record<string, number>);
  
  // By category
  const categoryStmt = db.prepare('SELECT inquiry_category, COUNT(*) as count FROM inquiries GROUP BY inquiry_category');
  const categoryRows = categoryStmt.all() as { inquiry_category: string; count: number }[];
  const byCategory = categoryRows.reduce((acc, row) => {
    acc[row.inquiry_category] = row.count;
    return acc;
  }, {} as Record<string, number>);
  
  // By urgency
  const urgencyStmt = db.prepare('SELECT urgency_level, COUNT(*) as count FROM inquiries GROUP BY urgency_level');
  const urgencyRows = urgencyStmt.all() as { urgency_level: string; count: number }[];
  const byUrgency = urgencyRows.reduce((acc, row) => {
    acc[row.urgency_level] = row.count;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    total,
    byStatus,
    byCategory,
    byUrgency
  };
}