// Industries Configuration
export interface IndustryConfig {
  id: string;
  translationKey: string;
}

// Centralized Industries Configuration
export const INDUSTRIES_CONFIG: IndustryConfig[] = [
  { id: 'manufacturing', translationKey: 'industry_manufacturing' },
  { id: 'construction', translationKey: 'industry_construction' },
  { id: 'healthcare', translationKey: 'industry_healthcare' },
  { id: 'oilgas', translationKey: 'industry_oilgas' },
  { id: 'transport', translationKey: 'industry_transport' },
  { id: 'warehousing', translationKey: 'industry_warehousing' },
  { id: 'foodbev', translationKey: 'industry_foodbev' },
  { id: 'utilities', translationKey: 'industry_utilities' }
];

// Industry Helper Functions
export const getIndustryById = (id: string): IndustryConfig | undefined => {
  return INDUSTRIES_CONFIG.find(industry => industry.id === id);
};

export const getIndustryTranslationKey = (id: string): string => {
  const industry = getIndustryById(id);
  return industry?.translationKey || `industry_${id}`;
};