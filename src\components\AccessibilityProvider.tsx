import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { validateThemeAccessibility, announceToScreenReader, createFocusManager } from '../utils/accessibility';
import { useTheme } from '../utils/themeUtils';

// Accessibility Context Type
interface AccessibilityContextType {
  announceMessage: (message: string, priority?: 'polite' | 'assertive') => void;
  focusManager: ReturnType<typeof createFocusManager>;
  prefersReducedMotion: boolean;
  highContrastMode: boolean;
  validateCurrentTheme: () => void;
}

// Create Accessibility Context
export const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

// Accessibility Provider Props
interface AccessibilityProviderProps {
  children: ReactNode;
}

// Skip Link Component
export const SkipLink: React.FC<{ href: string; children: ReactNode }> = ({ href, children }) => (
  <a href={href} className="skip-link">
    {children}
  </a>
);

// Screen Reader Only Component
export const ScreenReaderOnly: React.FC<{ children: ReactNode }> = ({ children }) => (
  <span className="sr-only">{children}</span>
);

// Live Region Component for announcements
export const LiveRegion: React.FC<{
  message: string;
  priority?: 'polite' | 'assertive';
  id?: string;
}> = ({ message, priority = 'polite', id = 'live-region' }) => (
  <div
    id={id}
    aria-live={priority}
    aria-atomic="true"
    className="sr-only"
  >
    {message}
  </div>
);

// Focus Trap Component
export const FocusTrap: React.FC<{
  children: ReactNode;
  active: boolean;
  restoreFocus?: boolean;
}> = ({ children, active, restoreFocus = true }) => {
  const { focusManager } = useAccessibility();
  const containerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!active || !containerRef.current) return;

    if (restoreFocus) {
      focusManager.saveFocus();
    }

    const cleanup = focusManager.trapFocus(containerRef.current);

    return () => {
      cleanup();
      if (restoreFocus) {
        focusManager.restoreFocus();
      }
    };
  }, [active, focusManager, restoreFocus]);

  return (
    <div ref={containerRef}>
      {children}
    </div>
  );
};

// Accessibility Provider Component
export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({ children }) => {
  const { effectiveTheme } = useTheme();
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [highContrastMode, setHighContrastMode] = useState(false);
  const focusManager = createFocusManager();

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, []);

  // Check for high contrast preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setHighContrastMode(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setHighContrastMode(e.matches);
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, []);

  // Validate theme accessibility
  const validateCurrentTheme = () => {
    const themeColors = {
      primary: effectiveTheme === 'dark' ? 'rgb(251 146 60)' : 'rgb(194 65 12)',
      secondary: effectiveTheme === 'dark' ? 'rgb(96 165 250)' : 'rgb(29 78 216)',
      background: effectiveTheme === 'dark' ? 'rgb(17 24 39)' : 'rgb(255 255 255)',
      surface: effectiveTheme === 'dark' ? 'rgb(31 41 55)' : 'rgb(249 250 251)',
      text: {
        primary: effectiveTheme === 'dark' ? 'rgb(249 250 251)' : 'rgb(17 24 39)',
        secondary: effectiveTheme === 'dark' ? 'rgb(209 213 219)' : 'rgb(55 65 81)',
        muted: effectiveTheme === 'dark' ? 'rgb(156 163 175)' : 'rgb(107 114 128)',
      },
      accent: effectiveTheme === 'dark' ? 'rgb(52 211 153)' : 'rgb(5 150 105)',
    };

    const results = validateThemeAccessibility(themeColors);

    // Log validation results in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🎨 Theme Accessibility Validation (${effectiveTheme})`);
      Object.entries(results).forEach(([, result]) => {
        if (result.recommendation) {
          console.warn(`💡 ${result.recommendation}`);
        }
      });
      console.groupEnd();
    }

    return results;
  };

  // Announce message to screen readers
  const announceMessage = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announceToScreenReader(message, priority);
  };

  const contextValue: AccessibilityContextType = {
    announceMessage,
    focusManager,
    prefersReducedMotion,
    highContrastMode,
    validateCurrentTheme,
  };

  return (
    <AccessibilityContext.Provider value={contextValue}>
      {children}
      {/* Global live region for announcements */}
      <div
        id="global-live-region"
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />

    </AccessibilityContext.Provider>
  );
};

// Custom hook to use Accessibility context
export const useAccessibility = (): AccessibilityContextType => {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
};

// Higher-order component for accessible components
export const withAccessibility = (Component) => {
  const AccessibleComponent = (props) => {
    const accessibility = useAccessibility();
    return <Component {...props} accessibility={accessibility} />;
  };

  AccessibleComponent.displayName = `withAccessibility(${Component.displayName || Component.name})`;
  return AccessibleComponent;
};

// Accessible Button Component
export const AccessibleButton: React.FC<{
  children: ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  className?: string;
}> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  className = '',
}) => {
    const { prefersReducedMotion } = useAccessibility();

    const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-md
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${prefersReducedMotion ? '' : 'transition-colors'}
  `;

    const variantClasses = {
      primary: variant === 'primary' ? 'btn-primary' : '',
      secondary: variant === 'secondary' ? 'btn-secondary' : '',
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-5 py-2.5 text-sm',
      lg: 'px-6 py-3 text-base',
    };

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
      >
        {children}
      </button>
    );
  };

export default AccessibilityProvider;