import React, { createContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

// SEO Data Types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'service' | 'organization';
  structuredData?: {
    '@context': string;
    '@type': string;
    [key: string]: unknown;
  };
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: string;
    siteName?: string;
  };
  twitter?: {
    card?: 'summary' | 'summary_large_image';
    site?: string;
    creator?: string;
  };
  noIndex?: boolean;
}

// SEO Context Type
export interface SEOContextType {
  updateSEO: (seoData: Partial<SEOData>) => void;
  currentSEO: SEOData;
  resetSEO: () => void;
}

// Default SEO configuration
const getDefaultSEO = (language: string): SEOData => ({
  title: language === 'sq' 
    ? 'SafeWork Kosova - Siguria në Punë dhe Shëndetësia Profesionale'
    : 'SafeWork Kosova - Workplace Safety and Occupational Health',
  description: language === 'sq'
    ? 'SafeWork Kosova ofron shërbime profesionale për siguri në punë, vlerësim rreziku, trajnime dhe konsulencë për bizneset në Kosovë.'
    : 'SafeWork Kosova provides professional workplace safety services, risk assessments, training and consulting for businesses in Kosovo.',
  keywords: language === 'sq'
    ? ['siguria në punë', 'shëndetësia profesionale', 'vlerësim rreziku', 'trajnime siguria', 'konsulencë', 'Kosovë']
    : ['workplace safety', 'occupational health', 'risk assessment', 'safety training', 'consulting', 'Kosovo'],
  image: '/images/safework-kosova-og.png',
  type: 'website',
  openGraph: {
    siteName: 'SafeWork Kosova',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@safeworkkosova',
  },
});

// Create SEO Context
export const SEOContext = createContext<SEOContextType | undefined>(undefined);

// SEO Provider Props
interface SEOProviderProps {
  children: ReactNode;
}

// SEO Provider Component
export const SEOProvider: React.FC<SEOProviderProps> = ({ children }) => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const [currentSEO, setCurrentSEO] = useState<SEOData>(() => 
    getDefaultSEO(i18n.language)
  );

  // Update SEO when language changes
  useEffect(() => {
    const defaultSEO = getDefaultSEO(i18n.language);
    setCurrentSEO(prev => ({
      ...defaultSEO,
      ...prev,
      // Always update language-dependent defaults
      title: prev.title === getDefaultSEO(i18n.language === 'sq' ? 'en' : 'sq').title 
        ? defaultSEO.title 
        : prev.title,
      description: prev.description === getDefaultSEO(i18n.language === 'sq' ? 'en' : 'sq').description 
        ? defaultSEO.description 
        : prev.description,
      keywords: prev.keywords?.length === 0 ? defaultSEO.keywords : prev.keywords,
    }));
  }, [i18n.language]);

  // Update URL when location changes
  useEffect(() => {
    const currentUrl = `${window.location.origin}${location.pathname}`;
    setCurrentSEO(prev => ({
      ...prev,
      url: currentUrl,
    }));
  }, [location]);

  // Update SEO function - memoized to prevent infinite re-renders
  const updateSEO = useCallback((seoData: Partial<SEOData>) => {
    setCurrentSEO(prev => ({
      ...prev,
      ...seoData,
      // Merge nested objects properly
      openGraph: seoData.openGraph
        ? { ...prev.openGraph, ...seoData.openGraph }
        : prev.openGraph,
      twitter: seoData.twitter
        ? { ...prev.twitter, ...seoData.twitter }
        : prev.twitter,
      structuredData: seoData.structuredData || prev.structuredData,
    }));
  }, []);

  // Reset SEO to defaults
  const resetSEO = () => {
    setCurrentSEO(getDefaultSEO(i18n.language));
  };

  const contextValue: SEOContextType = {
    updateSEO,
    currentSEO,
    resetSEO,
  };

  return (
    <SEOContext.Provider value={contextValue}>
      {children}
    </SEOContext.Provider>
  );
};

// Export types for use in other files
export type { SEOContextType, SEOData };

// Re-export hooks and utilities from utility file
export { useSEO, generateStructuredData, validateSEOData } from '../utils/seoUtils';