import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  createInquiry, 
  getInquiryById, 
  getInquiries, 
  updateInquiry, 
  getInquiryStats,
  type CreateInquiryData 
} from '../lib/inquiryService.js';
import { getDatabase, closeDatabase } from '../lib/database.js';
import type { Inquiry } from '../types/database.js';

describe('InquiryService', () => {
  beforeEach(() => {
    // Initialize database for each test
    const db = getDatabase();
    // Clear inquiries table
    db.exec('DELETE FROM inquiries');
  });

  afterEach(() => {
    closeDatabase();
  });

  describe('createInquiry', () => {
    it('should create a new inquiry with valid data', () => {
      const inquiryData: CreateInquiryData = {
        contactName: 'John <PERSON>',
        contactEmail: '<EMAIL>',
        companyName: 'Test Company',
        inquiryCategory: 'risk-assessment',
        inquirySubcategory: 'workplace hazards',
        urgencyLevel: 'medium',
        description: 'Need help with workplace risk assessment',
        requiresProfessional: true
      };

      const inquiry = createInquiry(inquiryData);

      expect(inquiry).toBeDefined();
      expect(inquiry.id).toBeDefined();
      expect(inquiry.contactName).toBe('John Doe');
      expect(inquiry.contactEmail).toBe('<EMAIL>');
      expect(inquiry.companyName).toBe('Test Company');
      expect(inquiry.inquiryCategory).toBe('risk-assessment');
      expect(inquiry.inquirySubcategory).toBe('workplace hazards');
      expect(inquiry.urgencyLevel).toBe('medium');
      expect(inquiry.description).toBe('Need help with workplace risk assessment');
      expect(inquiry.status).toBe('new');
      expect(inquiry.requiresProfessional).toBe(true);
      expect(inquiry.createdAt).toBeInstanceOf(Date);
      expect(inquiry.updatedAt).toBeInstanceOf(Date);
    });

    it('should create inquiry without optional fields', () => {
      const inquiryData: CreateInquiryData = {
        contactName: 'Jane Smith',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'general',
        urgencyLevel: 'low',
        description: 'General safety question'
      };

      const inquiry = createInquiry(inquiryData);

      expect(inquiry).toBeDefined();
      expect(inquiry.contactName).toBe('Jane Smith');
      expect(inquiry.contactEmail).toBe('<EMAIL>');
      expect(inquiry.companyName).toBeUndefined();
      expect(inquiry.inquirySubcategory).toBeUndefined();
      expect(inquiry.requiresProfessional).toBe(false);
    });

    it('should trim and normalize email', () => {
      const inquiryData: CreateInquiryData = {
        contactName: '  John Doe  ',
        contactEmail: '  <EMAIL>  ',
        inquiryCategory: 'training',
        urgencyLevel: 'high',
        description: 'Training inquiry'
      };

      const inquiry = createInquiry(inquiryData);

      expect(inquiry.contactName).toBe('John Doe');
      expect(inquiry.contactEmail).toBe('<EMAIL>');
    });
  });

  describe('getInquiryById', () => {
    it('should return inquiry by ID', () => {
      const inquiryData: CreateInquiryData = {
        contactName: 'Test User',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'compliance',
        urgencyLevel: 'medium',
        description: 'Compliance question'
      };

      const createdInquiry = createInquiry(inquiryData);
      const retrievedInquiry = getInquiryById(createdInquiry.id);

      expect(retrievedInquiry).toBeDefined();
      expect(retrievedInquiry!.id).toBe(createdInquiry.id);
      expect(retrievedInquiry!.contactName).toBe('Test User');
      expect(retrievedInquiry!.contactEmail).toBe('<EMAIL>');
    });

    it('should return null for non-existent ID', () => {
      const inquiry = getInquiryById('non-existent-id');
      expect(inquiry).toBeNull();
    });
  });

  describe('getInquiries', () => {
    beforeEach(() => {
      // Create test inquiries
      createInquiry({
        contactName: 'User 1',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'high',
        description: 'High priority risk assessment'
      });

      createInquiry({
        contactName: 'User 2',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'training',
        urgencyLevel: 'medium',
        description: 'Training request'
      });

      createInquiry({
        contactName: 'User 3',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'low',
        description: 'Low priority assessment'
      });
    });

    it('should return all inquiries without filters', () => {
      const inquiries = getInquiries();
      expect(inquiries).toHaveLength(3);
    });

    it('should filter by category', () => {
      const inquiries = getInquiries({ category: 'risk-assessment' });
      expect(inquiries).toHaveLength(2);
      expect(inquiries.every(i => i.inquiryCategory === 'risk-assessment')).toBe(true);
    });

    it('should filter by urgency level', () => {
      const inquiries = getInquiries({ urgencyLevel: 'high' });
      expect(inquiries).toHaveLength(1);
      expect(inquiries[0].urgencyLevel).toBe('high');
    });

    it('should filter by status', () => {
      const inquiries = getInquiries({ status: 'new' });
      expect(inquiries).toHaveLength(3);
      expect(inquiries.every(i => i.status === 'new')).toBe(true);
    });

    it('should apply limit and offset', () => {
      const inquiries = getInquiries({ limit: 2, offset: 1 });
      expect(inquiries).toHaveLength(2);
    });

    it('should return inquiries in descending order by creation date', () => {
      const inquiries = getInquiries();
      expect(inquiries).toHaveLength(3);
      
      for (let i = 1; i < inquiries.length; i++) {
        expect(inquiries[i - 1].createdAt.getTime()).toBeGreaterThanOrEqual(
          inquiries[i].createdAt.getTime()
        );
      }
    });
  });

  describe('updateInquiry', () => {
    let testInquiry: Inquiry;

    beforeEach(() => {
      testInquiry = createInquiry({
        contactName: 'Test User',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'general',
        urgencyLevel: 'medium',
        description: 'Test inquiry'
      });
    });

    it('should update inquiry status', () => {
      const updated = updateInquiry(testInquiry.id, { status: 'in-progress' });
      
      expect(updated).toBeDefined();
      expect(updated!.status).toBe('in-progress');
      expect(updated!.updatedAt.getTime()).toBeGreaterThan(testInquiry.updatedAt.getTime());
    });

    it('should update assigned user', () => {
      const updated = updateInquiry(testInquiry.id, { assignedTo: '<EMAIL>' });
      
      expect(updated).toBeDefined();
      expect(updated!.assignedTo).toBe('<EMAIL>');
    });

    it('should update requiresProfessional flag', () => {
      const updated = updateInquiry(testInquiry.id, { requiresProfessional: true });
      
      expect(updated).toBeDefined();
      expect(updated!.requiresProfessional).toBe(true);
    });

    it('should update multiple fields', () => {
      const updated = updateInquiry(testInquiry.id, {
        status: 'responded',
        assignedTo: '<EMAIL>',
        requiresProfessional: true
      });
      
      expect(updated).toBeDefined();
      expect(updated!.status).toBe('responded');
      expect(updated!.assignedTo).toBe('<EMAIL>');
      expect(updated!.requiresProfessional).toBe(true);
    });

    it('should return null for non-existent inquiry', () => {
      const updated = updateInquiry('non-existent-id', { status: 'closed' });
      expect(updated).toBeNull();
    });

    it('should return unchanged inquiry when no updates provided', () => {
      const updated = updateInquiry(testInquiry.id, {});
      
      expect(updated).toBeDefined();
      expect(updated!.status).toBe(testInquiry.status);
      expect(updated!.assignedTo).toBe(testInquiry.assignedTo);
    });
  });

  describe('getInquiryStats', () => {
    beforeEach(() => {
      // Create test inquiries with different statuses and categories
      createInquiry({
        contactName: 'User 1',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'high',
        description: 'Test 1'
      });

      const inquiry2 = createInquiry({
        contactName: 'User 2',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'training',
        urgencyLevel: 'medium',
        description: 'Test 2'
      });

      // Update one inquiry to have different status
      updateInquiry(inquiry2.id, { status: 'in-progress' });

      createInquiry({
        contactName: 'User 3',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'emergency',
        description: 'Test 3'
      });
    });

    it('should return correct statistics', () => {
      const stats = getInquiryStats();

      expect(stats.total).toBe(3);
      expect(stats.byStatus.new).toBe(2);
      expect(stats.byStatus['in-progress']).toBe(1);
      expect(stats.byCategory['risk-assessment']).toBe(2);
      expect(stats.byCategory.training).toBe(1);
      expect(stats.byUrgency.high).toBe(1);
      expect(stats.byUrgency.medium).toBe(1);
      expect(stats.byUrgency.emergency).toBe(1);
    });

    it('should return zero stats for empty database', () => {
      // Clear all inquiries
      const db = getDatabase();
      db.exec('DELETE FROM inquiries');

      const stats = getInquiryStats();

      expect(stats.total).toBe(0);
      expect(Object.keys(stats.byStatus)).toHaveLength(0);
      expect(Object.keys(stats.byCategory)).toHaveLength(0);
      expect(Object.keys(stats.byUrgency)).toHaveLength(0);
    });
  });
});