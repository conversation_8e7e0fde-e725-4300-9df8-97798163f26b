import { describe, it, expect } from 'vitest';
import { 
  validateInquiryData, 
  validateInquiryFilters,
  isValidEmail,
  isValidCategory,
  isValidUrgencyLevel,
  sanitizeString
} from '../lib/validation.js';

describe('Validation', () => {
  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('<EMAIL>')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });

    it('should reject emails that are too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      expect(isValidEmail(longEmail)).toBe(false);
    });
  });

  describe('isValidCategory', () => {
    it('should validate correct categories', () => {
      expect(isValidCategory('risk-assessment')).toBe(true);
      expect(isValidCategory('training')).toBe(true);
      expect(isValidCategory('compliance')).toBe(true);
      expect(isValidCategory('emergency')).toBe(true);
      expect(isValidCategory('general')).toBe(true);
    });

    it('should reject invalid categories', () => {
      expect(isValidCategory('invalid-category')).toBe(false);
      expect(isValidCategory('')).toBe(false);
      expect(isValidCategory('TRAINING')).toBe(false);
    });
  });

  describe('isValidUrgencyLevel', () => {
    it('should validate correct urgency levels', () => {
      expect(isValidUrgencyLevel('low')).toBe(true);
      expect(isValidUrgencyLevel('medium')).toBe(true);
      expect(isValidUrgencyLevel('high')).toBe(true);
      expect(isValidUrgencyLevel('emergency')).toBe(true);
    });

    it('should reject invalid urgency levels', () => {
      expect(isValidUrgencyLevel('invalid')).toBe(false);
      expect(isValidUrgencyLevel('')).toBe(false);
      expect(isValidUrgencyLevel('HIGH')).toBe(false);
    });
  });

  describe('sanitizeString', () => {
    it('should trim whitespace', () => {
      expect(sanitizeString('  hello world  ')).toBe('hello world');
    });

    it('should remove dangerous HTML characters', () => {
      expect(sanitizeString('hello<script>alert("xss")</script>')).toBe('helloscriptalert("xss")/script');
      expect(sanitizeString('test > value < here')).toBe('test  value  here');
    });

    it('should handle empty strings', () => {
      expect(sanitizeString('')).toBe('');
      expect(sanitizeString('   ')).toBe('');
    });
  });

  describe('validateInquiryData', () => {
    const validInquiryData = {
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      companyName: 'Test Company',
      inquiryCategory: 'risk-assessment',
      inquirySubcategory: 'workplace hazards',
      urgencyLevel: 'medium',
      description: 'Need help with workplace risk assessment',
      requiresProfessional: true
    };

    it('should validate correct inquiry data', () => {
      const result = validateInquiryData(validInquiryData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate minimal required data', () => {
      const minimalData = {
        contactName: 'John Doe',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'general',
        urgencyLevel: 'low',
        description: 'This is a test description with enough characters'
      };

      const result = validateInquiryData(minimalData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    describe('contactName validation', () => {
      it('should require contactName', () => {
        const data = { ...validInquiryData };
        delete data.contactName;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'contactName')).toBe(true);
      });

      it('should reject contactName that is too short', () => {
        const data = { ...validInquiryData, contactName: 'A' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'contactName' && e.message.includes('at least 2 characters'))).toBe(true);
      });

      it('should reject contactName that is too long', () => {
        const data = { ...validInquiryData, contactName: 'A'.repeat(101) };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'contactName' && e.message.includes('less than 100 characters'))).toBe(true);
      });
    });

    describe('contactEmail validation', () => {
      it('should require contactEmail', () => {
        const data = { ...validInquiryData };
        delete data.contactEmail;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'contactEmail')).toBe(true);
      });

      it('should reject invalid email format', () => {
        const data = { ...validInquiryData, contactEmail: 'invalid-email' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'contactEmail' && e.message.includes('valid email'))).toBe(true);
      });
    });

    describe('companyName validation', () => {
      it('should allow empty companyName', () => {
        const data = { ...validInquiryData };
        delete data.companyName;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(true);
      });

      it('should reject companyName that is too long', () => {
        const data = { ...validInquiryData, companyName: 'A'.repeat(201) };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'companyName')).toBe(true);
      });
    });

    describe('inquiryCategory validation', () => {
      it('should require inquiryCategory', () => {
        const data = { ...validInquiryData };
        delete data.inquiryCategory;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'inquiryCategory')).toBe(true);
      });

      it('should reject invalid category', () => {
        const data = { ...validInquiryData, inquiryCategory: 'invalid-category' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'inquiryCategory' && e.message.includes('Invalid'))).toBe(true);
      });
    });

    describe('urgencyLevel validation', () => {
      it('should require urgencyLevel', () => {
        const data = { ...validInquiryData };
        delete data.urgencyLevel;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'urgencyLevel')).toBe(true);
      });

      it('should reject invalid urgency level', () => {
        const data = { ...validInquiryData, urgencyLevel: 'invalid-level' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'urgencyLevel' && e.message.includes('Invalid'))).toBe(true);
      });
    });

    describe('description validation', () => {
      it('should require description', () => {
        const data = { ...validInquiryData };
        delete data.description;

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'description')).toBe(true);
      });

      it('should reject description that is too short', () => {
        const data = { ...validInquiryData, description: 'Short' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'description' && e.message.includes('at least 10 characters'))).toBe(true);
      });

      it('should reject description that is too long', () => {
        const data = { ...validInquiryData, description: 'A'.repeat(2001) };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'description' && e.message.includes('less than 2000 characters'))).toBe(true);
      });
    });

    describe('requiresProfessional validation', () => {
      it('should allow boolean values', () => {
        const data1 = { ...validInquiryData, requiresProfessional: true };
        const data2 = { ...validInquiryData, requiresProfessional: false };

        expect(validateInquiryData(data1).isValid).toBe(true);
        expect(validateInquiryData(data2).isValid).toBe(true);
      });

      it('should reject non-boolean values', () => {
        const data = { ...validInquiryData, requiresProfessional: 'yes' };

        const result = validateInquiryData(data);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(e => e.field === 'requiresProfessional')).toBe(true);
      });
    });
  });

  describe('validateInquiryFilters', () => {
    it('should validate empty filters', () => {
      const result = validateInquiryFilters({});
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate correct filters', () => {
      const filters = {
        status: 'new',
        category: 'risk-assessment',
        urgencyLevel: 'high',
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
        limit: '10',
        offset: '0'
      };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid status filter', () => {
      const filters = { status: 'invalid-status' };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'status')).toBe(true);
    });

    it('should reject invalid category filter', () => {
      const filters = { category: 'invalid-category' };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'category')).toBe(true);
    });

    it('should reject invalid date filters', () => {
      const filters = { 
        dateFrom: 'invalid-date',
        dateTo: 'also-invalid'
      };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'dateFrom')).toBe(true);
      expect(result.errors.some(e => e.field === 'dateTo')).toBe(true);
    });

    it('should reject invalid pagination parameters', () => {
      const filters = { 
        limit: '0',  // Too low
        offset: '-1' // Negative
      };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'limit')).toBe(true);
      expect(result.errors.some(e => e.field === 'offset')).toBe(true);
    });

    it('should reject limit that is too high', () => {
      const filters = { limit: '101' };

      const result = validateInquiryFilters(filters);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'limit')).toBe(true);
    });
  });
});