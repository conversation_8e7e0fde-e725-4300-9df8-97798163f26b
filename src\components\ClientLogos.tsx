import React from 'react';
// Import the useTranslation hook
import { useTranslation } from 'react-i18next';

const ClientLogos: React.FC = () => {
  // Initialize the translation hook and specify the 'home' namespace
  const { t } = useTranslation('home');

  // The client data now references translation keys for the industry
  const clients = [
    {
      name: 'Eurometal',
      industryKey: 'client_eurometal_industry',
      logo: '/images/client-eurometal.jpg'
    },
    {
      name: 'Egfedri',
      industryKey: 'client_egfedri_industry',
      logo: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgODAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMjE0N2FjO30uY2xzLTJ7ZmlsbDojZmY4YzAwO308L3N0eWxlPjwvZGVmcz48ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIj48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yMCw2MEg2MFYyMEgyMFptMTAtMzBIMzB2MTBoMTBWNDBINTBWMzBaIi8+PHBhdGggY2xhc3M9ImNscy0yIiBkPSJNNzAsMjBoNjB2MTBIODBWNDBoNTB2MTBIODBWNjBoNTB2MTBINzBaIi8+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMTQwLDIwaDYwdjEwSDE1MHYxMGg1MHYxMEgxNTB2MTBoNTB2MTBIMTQwWiIvPjxwYXRoIGNsYXNzPSJjbHMtMiIgZD0iTTIxMCwyMGgxMHY2MEgyMTBaIi8+PC9nPjwvc3ZnPg=='
    },
    {
      name: 'B.Bavaria',
      industryKey: 'client_bavaria_industry',
      logo: '/images/client-bavaria.svg'
    },
    {
      name: 'Integral Holding',
      industryKey: 'client_integral_industry',
      logo: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgODAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMDA0ZDk5O30uY2xzLTJ7ZmlsbDojZmY2NjAwO308L3N0eWxlPjwvZGVmcz48ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIj48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yMCwyMGg2MHY2MEgyMFptMTAsMTBIMzB2NDBINzBWMzBaIi8+PHBhdGggY2xhc3M9ImNscy0yIiBkPSJNOTAsMjBoMTB2NjBIOTBabTIwLDBoMTB2NjBIMTEwWm0yMCwwaDEwdjYwSDEzMFptMjAsMGgxMHY2MEgxNTBabTIwLDBoMTB2NjBIMTcwWm0yMCwwaDEwdjYwSDE5MFptMjAsMGgxMHY2MEgyMTBaIi8+PC9nPjwvc3ZnPg=='
    },
    {
      name: 'Crown Furniture',
      industryKey: 'client_crown_industry',
      logo: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgODAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojOGIzNDk5O30uY2xzLTJ7ZmlsbDojZmZkNzAwO308L3N0eWxlPjwvZGVmcz48ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIj48cGF0aCBjbGFzcz0iY2xzLTIiIGQ9Ik0xMjAsMjBsLTIwLDQwaDQwWiIvPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTQwLDIwQTIwLDIwLDAsMCwwLDIwLDQwYTIwLDIwLDAsMCwwLDIwLDIwaDQwVjIwWm0wLDMwQTEwLDEwLDAsMCwxLDMwLDQwYTEwLDEwLDAsMCwxLDEwLTEwSDYwVjUwWiIvPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTE2MCwyMHY0MGg0MGEyMCwyMCwwLDAsMCwyMC0yMCwyMCwyMCwwLDAsMC0yMC0yMFptNDAsMzBIMTgwVjMwaDIwYTEwLDEwLDAsMCwxLDAsMjBaIi8+PC9nPjwvc3ZnPg=='
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 items-center justify-items-center">
      {clients.map((client, index) => (
        <div key={index} className="w-full max-w-xs text-center">
          <div className="bg-white p-5 rounded-xl border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 hover:border-blue-100 h-36 w-full flex flex-col items-center justify-center transform hover:-translate-y-1">
            <div className="h-16 w-full flex items-center justify-center mb-3">
              <img
                src={client.logo}
                alt={`${client.name} logo`}
                className="max-h-16 max-w-full object-contain"
              />
            </div>
            <p className="font-bold text-gray-800 text-lg mb-1">{client.name}</p>
            {/* Translate the industry using the key */}
            <p className="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full inline-block">{t(client.industryKey)}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ClientLogos;