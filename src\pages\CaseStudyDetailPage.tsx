import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Check, Building2, Users, TrendingUp, Award } from 'lucide-react';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';
import caseStudies from '../data/caseStudies';

const CaseStudyDetailPage: React.FC = () => {
  const { caseStudyId } = useParams<{ caseStudyId: string }>();
  const { t, i18n } = useTranslation('case-studies');
  const currentLang = i18n.language;

  const caseStudyMaster = caseStudyId && caseStudies[caseStudyId] ? caseStudies[caseStudyId] : null;

  if (!caseStudyMaster) {
    return (
      <div className="min-h-[70vh] flex items-center justify-center px-4 py-12">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('detail_page_not_found')}</h1>
          <p className="text-gray-600 mb-8">{t('detail_page_not_found_desc')}</p>
          <Link to={`/${currentLang}/case-studies`} className="btn-primary">
            {t('detail_page_cta')}
          </Link>
        </div>
      </div>
    );
  }

  // Defensively fetch all data, providing empty fallbacks
  const caseStudy = {
    ...caseStudyMaster,
    title: t(`${caseStudyMaster.id}.title`, 'Title not found'),
    subtitle: t(`${caseStudyMaster.id}.subtitle`, 'Subtitle not found'),
    industry: t(`${caseStudyMaster.id}.industry`, 'Industry not found'),
    challenge: (t(`${caseStudyMaster.id}.challenge`, { returnObjects: true }) as string[]) || [],
    solution: (t(`${caseStudyMaster.id}.solution`, { returnObjects: true }) as string[]) || [],
    results: (t(`${caseStudyMaster.id}.results`, { returnObjects: true }) as string[]) || [],
    testimonial: {
      quote: t(`${caseStudyMaster.id}.testimonial.quote`, 'Testimonial not found.'),
      author: t(`${caseStudyMaster.id}.testimonial.author`, 'Author not found'),
      position: t(`${caseStudyMaster.id}.testimonial.position`, 'Position not found'),
      company: caseStudyMaster.testimonialCompany || ''
    }
  };

  return (
    <div>
      <Hero 
        title={caseStudy.title}
        subtitle={caseStudy.subtitle}
        ctaText={t('sidebar_cta_button')}
        ctaLink="/contact"
        bgImage={caseStudy.heroImage}
      />

      <div className="bg-white border-b">
        <div className="container mx-auto px-4 md:px-6 py-4">
          <Link to={`/${currentLang}/case-studies`} className="inline-flex items-center text-blue-800 hover:text-orange-500 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('back_to_list')}
          </Link>
        </div>
      </div>

      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col lg:flex-row gap-12">
            <div className="lg:w-2/3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                <div className="bg-gray-50 p-4 rounded-lg"><Building2 className="w-8 h-8 text-blue-800 mb-2" /><p className="font-medium text-gray-600">{t('sidebar_client')}</p><p className="text-gray-900 font-semibold">{caseStudy.client}</p></div>
                <div className="bg-gray-50 p-4 rounded-lg"><Users className="w-8 h-8 text-blue-800 mb-2" /><p className="font-medium text-gray-600">{t('sidebar_industry')}</p><p className="text-gray-900 font-semibold">{caseStudy.industry}</p></div>
                <div className="bg-gray-50 p-4 rounded-lg"><TrendingUp className="w-8 h-8 text-blue-800 mb-2" /><p className="font-medium text-gray-600">{t('sidebar_duration')}</p><p className="text-gray-900 font-semibold">{caseStudy.duration}</p></div>
                <div className="bg-gray-50 p-4 rounded-lg"><Award className="w-8 h-8 text-blue-800 mb-2" /><p className="font-medium text-gray-600">{t('sidebar_location')}</p><p className="text-gray-900 font-semibold">{caseStudy.location}</p></div>
              </div>

              <div className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">{t('section_challenge')}</h2>
                <ul className="space-y-4">{caseStudy.challenge.map((item, index) => (<li key={index} className="flex items-start"><Check className="w-5 h-5 text-orange-500 mt-1 mr-2 flex-shrink-0" /><span className="text-gray-600">{item}</span></li>))}</ul>
              </div>

              <div className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">{t('section_solution')}</h2>
                <ul className="space-y-4">{caseStudy.solution.map((item, index) => (<li key={index} className="flex items-start"><Check className="w-5 h-5 text-green-500 mt-1 mr-2 flex-shrink-0" /><span className="text-gray-600">{item}</span></li>))}</ul>
              </div>

              <div className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">{t('section_results')}</h2>
                <ul className="space-y-4">{caseStudy.results.map((item, index) => (<li key={index} className="flex items-start"><Check className="w-5 h-5 text-blue-500 mt-1 mr-2 flex-shrink-0" /><span className="text-gray-600">{item}</span></li>))}</ul>
              </div>
            </div>

            <div className="lg:w-1/3">
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
                <blockquote className="text-lg text-gray-700 italic mb-4">"{caseStudy.testimonial.quote}"</blockquote>
                <div className="flex items-center"><div><p className="font-bold text-gray-900">{caseStudy.testimonial.author}</p><p className="text-sm text-gray-600">{caseStudy.testimonial.position}, {caseStudy.testimonial.company}</p></div></div>
              </div>
              <div className="bg-blue-800 text-white p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">{t('sidebar_cta_title')}</h3>
                <p className="mb-6">{t('sidebar_cta_desc')}</p>
                <Link to={`/${currentLang}/contact`} className="block text-center bg-white text-blue-800 py-3 px-6 rounded-md font-medium hover:bg-gray-100 transition-colors">{t('sidebar_cta_button')}</Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      <CtaSection title={t('cta_title')} description={t('cta_subtitle')} ctaText={t('cta_button')} ctaLink="/contact" />
    </div>
  );
};

export default CaseStudyDetailPage;