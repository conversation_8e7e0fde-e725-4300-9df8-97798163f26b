import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useSEO } from '../contexts/SEOContext';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  structuredData?: Record<string, unknown>;
  noIndex?: boolean;
  canonicalUrl?: string;
  type?: 'website' | 'article' | 'service' | 'organization';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: string;
    siteName?: string;
    locale?: string;
  };
  twitter?: {
    card?: 'summary' | 'summary_large_image';
    site?: string;
    creator?: string;
    title?: string;
    description?: string;
    image?: string;
  };
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  image,
  structuredData,
  noIndex,
  canonicalUrl,
  type,
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
  openGraph,
  twitter,
}) => {
  const { currentSEO } = useSEO();
  const { i18n } = useTranslation();

  // Merge props with context data, props take precedence
  const seoData = {
    title: title || currentSEO.title,
    description: description || currentSEO.description,
    keywords: keywords || currentSEO.keywords,
    image: image || currentSEO.image,
    url: canonicalUrl || currentSEO.url || window.location.href,
    type: type || currentSEO.type || 'website',
    openGraph: {
      ...currentSEO.openGraph,
      ...openGraph,
      title: openGraph?.title || title || currentSEO.openGraph?.title || currentSEO.title,
      description: openGraph?.description || description || currentSEO.openGraph?.description || currentSEO.description,
      image: openGraph?.image || image || currentSEO.openGraph?.image || currentSEO.image,
      type: openGraph?.type || type || currentSEO.openGraph?.type || currentSEO.type || 'website',
      siteName: openGraph?.siteName || currentSEO.openGraph?.siteName,
      locale: openGraph?.locale || i18n.language,
    },
    twitter: {
      ...currentSEO.twitter,
      ...twitter,
      title: twitter?.title || title || currentSEO.title,
      description: twitter?.description || description || currentSEO.description,
      image: twitter?.image || image || currentSEO.image,
    },
    structuredData: structuredData || currentSEO.structuredData,
    noIndex: noIndex !== undefined ? noIndex : currentSEO.noIndex,
    publishedTime,
    modifiedTime,
    author,
    section,
    tags,
  };

  // Generate alternate language URLs for hreflang
  const generateAlternateUrls = () => {
    const currentPath = window.location.pathname;
    const baseUrl = window.location.origin;
    
    // Remove language prefix if present
    const pathWithoutLang = currentPath.replace(/^\/(sq|en)/, '') || '/';
    
    return [
      { hreflang: 'sq', href: `${baseUrl}/sq${pathWithoutLang}` },
      { hreflang: 'en', href: `${baseUrl}/en${pathWithoutLang}` },
      { hreflang: 'x-default', href: `${baseUrl}${pathWithoutLang}` },
    ];
  };

  const alternateUrls = generateAlternateUrls();

  // Helper function to ensure absolute URLs for images
  const getAbsoluteUrl = (url: string) => {
    if (!url) return '';
    return url.startsWith('http') ? url : `${window.location.origin}${url}`;
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      
      {/* Keywords */}
      {seoData.keywords && seoData.keywords.length > 0 && (
        <meta name="keywords" content={seoData.keywords.join(', ')} />
      )}
      
      {/* Author */}
      {seoData.author && <meta name="author" content={seoData.author} />}
      
      {/* Robots */}
      {seoData.noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow" />
      )}
      
      {/* Canonical URL */}
      <link rel="canonical" href={seoData.url} />
      
      {/* Hreflang Tags */}
      {alternateUrls.map(({ hreflang, href }) => (
        <link
          key={hreflang}
          rel="alternate"
          hrefLang={hreflang}
          href={href}
        />
      ))}
      
      {/* Language */}
      <html lang={i18n.language} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={seoData.openGraph.title} />
      <meta property="og:description" content={seoData.openGraph.description} />
      <meta property="og:type" content={seoData.openGraph.type} />
      <meta property="og:url" content={seoData.url} />
      <meta property="og:locale" content={seoData.openGraph.locale} />
      
      {seoData.openGraph.siteName && (
        <meta property="og:site_name" content={seoData.openGraph.siteName} />
      )}
      
      {seoData.openGraph.image && (
        <>
          <meta property="og:image" content={getAbsoluteUrl(seoData.openGraph.image)} />
          <meta property="og:image:alt" content={seoData.openGraph.title} />
          <meta property="og:image:width" content="1200" />
          <meta property="og:image:height" content="630" />
        </>
      )}
      
      {/* Article-specific Open Graph tags */}
      {seoData.type === 'article' && (
        <>
          {seoData.publishedTime && (
            <meta property="article:published_time" content={seoData.publishedTime} />
          )}
          {seoData.modifiedTime && (
            <meta property="article:modified_time" content={seoData.modifiedTime} />
          )}
          {seoData.author && (
            <meta property="article:author" content={seoData.author} />
          )}
          {seoData.section && (
            <meta property="article:section" content={seoData.section} />
          )}
          {seoData.tags && seoData.tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={seoData.twitter.card || 'summary_large_image'} />
      
      {seoData.twitter.site && (
        <meta name="twitter:site" content={seoData.twitter.site} />
      )}
      
      {seoData.twitter.creator && (
        <meta name="twitter:creator" content={seoData.twitter.creator} />
      )}
      
      <meta name="twitter:title" content={seoData.twitter.title} />
      <meta name="twitter:description" content={seoData.twitter.description} />
      
      {seoData.twitter.image && (
        <meta name="twitter:image" content={getAbsoluteUrl(seoData.twitter.image)} />
      )}
      
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Theme color for mobile browsers */}
      <meta name="theme-color" content="#1e40af" />
      <meta name="msapplication-TileColor" content="#1e40af" />
      
      {/* Structured Data (JSON-LD) */}
      {seoData.structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(seoData.structuredData, null, 2)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;