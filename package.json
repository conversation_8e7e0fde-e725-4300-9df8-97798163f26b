{"name": "safework-kosova-website", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 5173", "dev:both": "concurrently \"npm run dev:api\" \"npm run dev\"", "dev:api": "vercel dev --listen 3000", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "dependencies": {"@react-google-maps/api": "^2.20.7", "@types/better-sqlite3": "^7.6.13", "@types/dompurify": "^3.0.5", "@types/sqlite3": "^3.1.11", "better-sqlite3": "^12.4.1", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.3", "react-router-dom": "^6.30.0", "resend": "^4.6.0", "sqlite3": "^5.1.7", "vite-plugin-sitemap": "^0.8.2", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/node": "^24.3.0", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "jsdom": "^26.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "vite": "^5.2.0", "vitest": "^3.2.4"}}