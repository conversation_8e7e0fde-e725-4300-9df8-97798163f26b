import { useContext } from 'react';
import { SEOContext, SEOContextType, SEOData } from '../contexts/SEOContext';

// Custom hook to use SEO context
export const useSEO = (): SEOContextType => {
  const context = useContext(SEOContext);
  if (!context) {
    throw new Error('useSEO must be used within a SEOProvider');
  }
  return context;
};

// Helper function to generate structured data
export const generateStructuredData = (type: string, data: Record<string, unknown>) => {
  return {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };
};

// Helper function to validate SEO data
export const validateSEOData = (seoData: Partial<SEOData>): string[] => {
  const errors: string[] = [];
  
  // Title validation
  if (!seoData.title) {
    errors.push('SEO title is required');
  } else if (seoData.title.length > 60) {
    errors.push('SEO title should be 60 characters or less');
  }
  
  // Description validation
  if (!seoData.description) {
    errors.push('SEO description is required');
  } else if (seoData.description.length > 160) {
    errors.push('SEO description should be 160 characters or less');
  }
  
  return errors;
};