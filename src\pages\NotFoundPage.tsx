import React from 'react';
import { Link } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-[70vh] flex items-center justify-center px-4 py-12">
      <div className="text-center">
        <div className="inline-block p-5 bg-blue-100 rounded-full mb-6">
          <AlertTriangle className="w-16 h-16 text-blue-800" />
        </div>
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">Faqja Nuk u Gjet</h2>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          Faqja që po kërkoni mund të jetë hequr, t'i jetë ndryshuar emri, ose <PERSON><PERSON>t<PERSON> pë<PERSON>ohë<PERSON>ht e padisponueshme.
        </p>
        <Link 
          to="/" 
          className="inline-flex items-center justify-center bg-blue-800 text-white rounded-md py-3 px-6 font-medium hover:bg-blue-900 transition-colors"
        >
          Kthehu në Faqen Kryesore
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
