// Theme Constants and Configuration
// This file contains comprehensive theme configuration objects, TypeScript types, and validation utilities

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';
export type EffectiveTheme = 'light' | 'dark';

// Color Palette Interface
export interface ColorPalette {
  primary: ColorScale;
  secondary: ColorScale;
  neutral: ColorScale;
  background: string;
  surface: SurfaceColors;
  text: TextColors;
  border: BorderColors;
  accent: AccentColors;
  status: StatusColors;
}

// Color Scale Interface (50-950 scale)
export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

// Surface Colors Interface
export interface SurfaceColors {
  default: string;
  variant: string;
  hover: string;
}

// Text Colors Interface
export interface TextColors {
  primary: string;
  secondary: string;
  muted: string;
  inverse: string;
}

// Border Colors Interface
export interface BorderColors {
  default: string;
  hover: string;
}

// Accent Colors Interface
export interface AccentColors {
  default: string;
  hover: string;
}

// Status Colors Interface
export interface StatusColors {
  success: StatusColor;
  warning: StatusColor;
  error: StatusColor;
  info: StatusColor;
}

// Individual Status Color Interface
export interface StatusColor {
  default: string;
  background: string;
}

// Spacing Configuration Interface
export interface SpacingConfig {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

// Border Radius Configuration Interface
export interface BorderRadiusConfig {
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

// Shadow Configuration Interface
export interface ShadowConfig {
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

// Transition Configuration Interface
export interface TransitionConfig {
  theme: string;
  fast: string;
  normal: string;
  slow: string;
}

// Z-Index Configuration Interface
export interface ZIndexConfig {
  dropdown: number;
  sticky: number;
  fixed: number;
  modalBackdrop: number;
  modal: number;
  popover: number;
  tooltip: number;
  toast: number;
}

// Complete Theme Configuration Interface
export interface ThemeConfig {
  colors: {
    light: ColorPalette;
    dark: ColorPalette;
  };
  spacing: SpacingConfig;
  borderRadius: BorderRadiusConfig;
  shadows: ShadowConfig;
  transitions: TransitionConfig;
  zIndex: ZIndexConfig;
}

// Light Theme Color Palette
export const lightColorPalette: ColorPalette = {
  primary: {
    50: 'rgb(255, 247, 237)',
    100: 'rgb(255, 237, 213)',
    200: 'rgb(254, 215, 170)',
    300: 'rgb(253, 186, 116)',
    400: 'rgb(251, 146, 60)',
    500: 'rgb(249, 115, 22)',
    600: 'rgb(234, 88, 12)',
    700: 'rgb(194, 65, 12)',
    800: 'rgb(154, 52, 18)',
    900: 'rgb(124, 45, 18)',
    950: 'rgb(67, 20, 7)',
  },
  secondary: {
    50: 'rgb(239, 246, 255)',
    100: 'rgb(219, 234, 254)',
    200: 'rgb(191, 219, 254)',
    300: 'rgb(147, 197, 253)',
    400: 'rgb(96, 165, 250)',
    500: 'rgb(59, 130, 246)',
    600: 'rgb(37, 99, 235)',
    700: 'rgb(29, 78, 216)',
    800: 'rgb(30, 64, 175)',
    900: 'rgb(30, 58, 138)',
    950: 'rgb(23, 37, 84)',
  },
  neutral: {
    50: 'rgb(249, 250, 251)',
    100: 'rgb(243, 244, 246)',
    200: 'rgb(229, 231, 235)',
    300: 'rgb(209, 213, 219)',
    400: 'rgb(156, 163, 175)',
    500: 'rgb(107, 114, 128)',
    600: 'rgb(75, 85, 99)',
    700: 'rgb(55, 65, 81)',
    800: 'rgb(31, 41, 55)',
    900: 'rgb(17, 24, 39)',
    950: 'rgb(3, 7, 18)',
  },
  background: 'rgb(255, 255, 255)',
  surface: {
    default: 'rgb(249, 250, 251)',
    variant: 'rgb(243, 244, 246)',
    hover: 'rgb(229, 231, 235)',
  },
  text: {
    primary: 'rgb(17, 24, 39)',
    secondary: 'rgb(75, 85, 99)',
    muted: 'rgb(156, 163, 175)',
    inverse: 'rgb(255, 255, 255)',
  },
  border: {
    default: 'rgb(229, 231, 235)',
    hover: 'rgb(209, 213, 219)',
  },
  accent: {
    default: 'rgb(16, 185, 129)',
    hover: 'rgb(5, 150, 105)',
  },
  status: {
    success: {
      default: 'rgb(34, 197, 94)',
      background: 'rgb(240, 253, 244)',
    },
    warning: {
      default: 'rgb(245, 158, 11)',
      background: 'rgb(255, 251, 235)',
    },
    error: {
      default: 'rgb(239, 68, 68)',
      background: 'rgb(254, 242, 242)',
    },
    info: {
      default: 'rgb(59, 130, 246)',
      background: 'rgb(239, 246, 255)',
    },
  },
};

// Dark Theme Color Palette
export const darkColorPalette: ColorPalette = {
  primary: {
    50: 'rgb(67, 20, 7)',
    100: 'rgb(124, 45, 18)',
    200: 'rgb(154, 52, 18)',
    300: 'rgb(194, 65, 12)',
    400: 'rgb(234, 88, 12)',
    500: 'rgb(249, 115, 22)',
    600: 'rgb(251, 146, 60)',
    700: 'rgb(253, 186, 116)',
    800: 'rgb(254, 215, 170)',
    900: 'rgb(255, 237, 213)',
    950: 'rgb(255, 247, 237)',
  },
  secondary: {
    50: 'rgb(23, 37, 84)',
    100: 'rgb(30, 58, 138)',
    200: 'rgb(30, 64, 175)',
    300: 'rgb(29, 78, 216)',
    400: 'rgb(37, 99, 235)',
    500: 'rgb(59, 130, 246)',
    600: 'rgb(96, 165, 250)',
    700: 'rgb(147, 197, 253)',
    800: 'rgb(191, 219, 254)',
    900: 'rgb(219, 234, 254)',
    950: 'rgb(239, 246, 255)',
  },
  neutral: {
    50: 'rgb(3, 7, 18)',
    100: 'rgb(17, 24, 39)',
    200: 'rgb(31, 41, 55)',
    300: 'rgb(55, 65, 81)',
    400: 'rgb(75, 85, 99)',
    500: 'rgb(107, 114, 128)',
    600: 'rgb(156, 163, 175)',
    700: 'rgb(209, 213, 219)',
    800: 'rgb(229, 231, 235)',
    900: 'rgb(243, 244, 246)',
    950: 'rgb(249, 250, 251)',
  },
  background: 'rgb(17, 24, 39)',
  surface: {
    default: 'rgb(31, 41, 55)',
    variant: 'rgb(55, 65, 81)',
    hover: 'rgb(75, 85, 99)',
  },
  text: {
    primary: 'rgb(249, 250, 251)',
    secondary: 'rgb(209, 213, 219)',
    muted: 'rgb(156, 163, 175)',
    inverse: 'rgb(17, 24, 39)',
  },
  border: {
    default: 'rgb(75, 85, 99)',
    hover: 'rgb(107, 114, 128)',
  },
  accent: {
    default: 'rgb(52, 211, 153)',
    hover: 'rgb(16, 185, 129)',
  },
  status: {
    success: {
      default: 'rgb(52, 211, 153)',
      background: 'rgb(6, 78, 59)',
    },
    warning: {
      default: 'rgb(251, 191, 36)',
      background: 'rgb(120, 53, 15)',
    },
    error: {
      default: 'rgb(248, 113, 113)',
      background: 'rgb(127, 29, 29)',
    },
    info: {
      default: 'rgb(96, 165, 250)',
      background: 'rgb(30, 58, 138)',
    },
  },
};

// Spacing Configuration
export const spacingConfig: SpacingConfig = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '3rem',
};

// Border Radius Configuration
export const borderRadiusConfig: BorderRadiusConfig = {
  sm: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
};

// Light Theme Shadow Configuration
export const lightShadowConfig: ShadowConfig = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
};

// Dark Theme Shadow Configuration
export const darkShadowConfig: ShadowConfig = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.3)',
};

// Transition Configuration
export const transitionConfig: TransitionConfig = {
  theme: '200ms ease-in-out',
  fast: '150ms ease-in-out',
  normal: '200ms ease-in-out',
  slow: '300ms ease-in-out',
};

// Z-Index Configuration
export const zIndexConfig: ZIndexConfig = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
};

// Complete Default Theme Configuration
export const defaultThemeConfig: ThemeConfig = {
  colors: {
    light: lightColorPalette,
    dark: darkColorPalette,
  },
  spacing: spacingConfig,
  borderRadius: borderRadiusConfig,
  shadows: lightShadowConfig, // Default to light shadows, dark shadows applied via CSS
  transitions: transitionConfig,
  zIndex: zIndexConfig,
};

// Theme Validation Utilities
export class ThemeValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ThemeValidationError';
  }
}

// Validate Color Scale
export const validateColorScale = (colorScale: Record<string, string>, scaleName: string): ColorScale => {
  const requiredShades = ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'];
  
  if (!colorScale || typeof colorScale !== 'object') {
    throw new ThemeValidationError(`${scaleName} color scale must be an object`);
  }
  
  for (const shade of requiredShades) {
    if (!colorScale[shade] || typeof colorScale[shade] !== 'string') {
      throw new ThemeValidationError(`${scaleName} color scale missing or invalid shade: ${shade}`);
    }
    
    // Basic RGB color validation
    if (!colorScale[shade].match(/^rgb\(\d+,\s*\d+,\s*\d+\)$/)) {
      throw new ThemeValidationError(`${scaleName} shade ${shade} must be in rgb(r, g, b) format`);
    }
  }
  
  return colorScale as ColorScale;
};

// Validate Color Palette
export const validateColorPalette = (palette: Partial<ColorPalette>, paletteName: string): ColorPalette => {
  if (!palette || typeof palette !== 'object') {
    throw new ThemeValidationError(`${paletteName} palette must be an object`);
  }
  
  // Validate color scales
  validateColorScale(palette.primary, `${paletteName}.primary`);
  validateColorScale(palette.secondary, `${paletteName}.secondary`);
  validateColorScale(palette.neutral, `${paletteName}.neutral`);
  
  // Validate semantic colors
  const requiredColors = ['background'];
  for (const color of requiredColors) {
    if (!palette[color] || typeof palette[color] !== 'string') {
      throw new ThemeValidationError(`${paletteName} palette missing or invalid color: ${color}`);
    }
  }
  
  // Validate nested color objects
  const nestedColorObjects = ['surface', 'text', 'border', 'accent', 'status'];
  for (const obj of nestedColorObjects) {
    if (!palette[obj] || typeof palette[obj] !== 'object') {
      throw new ThemeValidationError(`${paletteName} palette missing or invalid color object: ${obj}`);
    }
  }
  
  return palette as ColorPalette;
};

// Validate Theme Configuration
export const validateThemeConfig = (config: Partial<ThemeConfig>): ThemeConfig => {
  if (!config || typeof config !== 'object') {
    throw new ThemeValidationError('Theme configuration must be an object');
  }
  
  // Validate colors
  if (!config.colors || typeof config.colors !== 'object') {
    throw new ThemeValidationError('Theme configuration must include colors object');
  }
  
  validateColorPalette(config.colors.light, 'light');
  validateColorPalette(config.colors.dark, 'dark');
  
  // Validate other configuration objects
  const requiredObjects = ['spacing', 'borderRadius', 'shadows', 'transitions', 'zIndex'];
  for (const obj of requiredObjects) {
    if (!config[obj] || typeof config[obj] !== 'object') {
      throw new ThemeValidationError(`Theme configuration missing or invalid object: ${obj}`);
    }
  }
  
  return config as ThemeConfig;
};

// Get Theme-Specific Configuration
export const getThemeConfig = (theme: EffectiveTheme, config: ThemeConfig = defaultThemeConfig): ColorPalette => {
  try {
    validateThemeConfig(config);
    return config.colors[theme];
  } catch (error) {
    console.warn('Theme configuration validation failed, using default:', error);
    return defaultThemeConfig.colors[theme];
  }
};

// Theme Storage Key
export const THEME_STORAGE_KEY = 'safework-theme-preference';

// CSS Custom Property Names
export const CSS_CUSTOM_PROPERTIES = {
  // Primary colors
  PRIMARY_50: '--color-primary-50',
  PRIMARY_100: '--color-primary-100',
  PRIMARY_200: '--color-primary-200',
  PRIMARY_300: '--color-primary-300',
  PRIMARY_400: '--color-primary-400',
  PRIMARY_500: '--color-primary-500',
  PRIMARY_600: '--color-primary-600',
  PRIMARY_700: '--color-primary-700',
  PRIMARY_800: '--color-primary-800',
  PRIMARY_900: '--color-primary-900',
  PRIMARY_950: '--color-primary-950',
  
  // Secondary colors
  SECONDARY_50: '--color-secondary-50',
  SECONDARY_100: '--color-secondary-100',
  SECONDARY_200: '--color-secondary-200',
  SECONDARY_300: '--color-secondary-300',
  SECONDARY_400: '--color-secondary-400',
  SECONDARY_500: '--color-secondary-500',
  SECONDARY_600: '--color-secondary-600',
  SECONDARY_700: '--color-secondary-700',
  SECONDARY_800: '--color-secondary-800',
  SECONDARY_900: '--color-secondary-900',
  SECONDARY_950: '--color-secondary-950',
  
  // Neutral colors
  NEUTRAL_50: '--color-neutral-50',
  NEUTRAL_100: '--color-neutral-100',
  NEUTRAL_200: '--color-neutral-200',
  NEUTRAL_300: '--color-neutral-300',
  NEUTRAL_400: '--color-neutral-400',
  NEUTRAL_500: '--color-neutral-500',
  NEUTRAL_600: '--color-neutral-600',
  NEUTRAL_700: '--color-neutral-700',
  NEUTRAL_800: '--color-neutral-800',
  NEUTRAL_900: '--color-neutral-900',
  NEUTRAL_950: '--color-neutral-950',
  
  // Semantic colors
  BACKGROUND: '--color-background',
  SURFACE: '--color-surface',
  SURFACE_VARIANT: '--color-surface-variant',
  SURFACE_HOVER: '--color-surface-hover',
  BORDER: '--color-border',
  BORDER_HOVER: '--color-border-hover',
  ACCENT: '--color-accent',
  ACCENT_HOVER: '--color-accent-hover',
  
  // Text colors
  TEXT_PRIMARY: '--color-text-primary',
  TEXT_SECONDARY: '--color-text-secondary',
  TEXT_MUTED: '--color-text-muted',
  TEXT_INVERSE: '--color-text-inverse',
  
  // Status colors
  SUCCESS: '--color-success',
  SUCCESS_BG: '--color-success-bg',
  WARNING: '--color-warning',
  WARNING_BG: '--color-warning-bg',
  ERROR: '--color-error',
  ERROR_BG: '--color-error-bg',
  INFO: '--color-info',
  INFO_BG: '--color-info-bg',
  
  // Transitions
  TRANSITION_THEME: '--transition-theme',
  TRANSITION_FAST: '--transition-fast',
  TRANSITION_NORMAL: '--transition-normal',
  TRANSITION_SLOW: '--transition-slow',
} as const;

// Helper function to get CSS custom property value
export const getCSSCustomProperty = (property: string): string => {
  if (typeof document === 'undefined') return '';
  return getComputedStyle(document.documentElement).getPropertyValue(property).trim();
};

// Helper function to set CSS custom property value
export const setCSSCustomProperty = (property: string, value: string): void => {
  if (typeof document === 'undefined') return;
  document.documentElement.style.setProperty(property, value);
};

// Helper function to remove CSS custom property
export const removeCSSCustomProperty = (property: string): void => {
  if (typeof document === 'undefined') return;
  document.documentElement.style.removeProperty(property);
};

// Theme utility functions
export const themeUtils = {
  // Check if a theme mode is valid
  isValidThemeMode: (mode: string): mode is ThemeMode => {
    return ['light', 'dark', 'system'].includes(mode);
  },
  
  // Check if an effective theme is valid
  isValidEffectiveTheme: (theme: string): theme is EffectiveTheme => {
    return ['light', 'dark'].includes(theme);
  },
  
  // Get opposite theme
  getOppositeTheme: (theme: EffectiveTheme): EffectiveTheme => {
    return theme === 'light' ? 'dark' : 'light';
  },
  
  // Check if system theme is supported
  isSystemThemeSupported: (): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      return window.matchMedia('(prefers-color-scheme: dark)').matches !== undefined;
    } catch {
      return false;
    }
  },
  
  // Get system theme preference
  getSystemTheme: (): EffectiveTheme => {
    if (typeof window === 'undefined') return 'light';
    try {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    } catch {
      return 'light';
    }
  },
  
  // Check if reduced motion is preferred
  prefersReducedMotion: (): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    } catch {
      return false;
    }
  },
};

export default defaultThemeConfig;