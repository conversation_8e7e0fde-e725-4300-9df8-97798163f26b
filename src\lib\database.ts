import Database from 'better-sqlite3';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import type { 
  Inquiry, 
  Resource, 
  UserActivity, 
  InquiryRow, 
  ResourceRow, 
  UserActivityRow 
} from '../types/database.js';

// Database configuration
const DB_DIR = process.env.NODE_ENV === 'production' ? '/tmp' : './data';
const DB_PATH = join(DB_DIR, 'crm.db');

// Ensure database directory exists
if (!existsSync(DB_DIR)) {
  mkdirSync(DB_DIR, { recursive: true });
}

// Database instance
let db: Database.Database | null = null;

/**
 * Get database instance (singleton pattern)
 */
export function getDatabase(): Database.Database {
  if (!db) {
    db = new Database(DB_PATH);
    db.pragma('journal_mode = WAL');
    db.pragma('foreign_keys = ON');
    
    // Initialize schema if needed
    initializeSchema();
  }
  return db;
}

/**
 * Close database connection
 */
export function closeDatabase(): void {
  if (db) {
    db.close();
    db = null;
  }
}

/**
 * Initialize database schema
 */
function initializeSchema(): void {
  const database = getDatabase();
  
  // Create inquiries table
  database.exec(`
    CREATE TABLE IF NOT EXISTS inquiries (
      id TEXT PRIMARY KEY,
      contact_name TEXT NOT NULL,
      contact_email TEXT NOT NULL,
      company_name TEXT,
      inquiry_category TEXT NOT NULL,
      inquiry_subcategory TEXT,
      urgency_level TEXT NOT NULL,
      description TEXT NOT NULL,
      status TEXT DEFAULT 'new',
      assigned_to TEXT,
      requires_professional INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create resources table
  database.exec(`
    CREATE TABLE IF NOT EXISTS resources (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      category TEXT NOT NULL,
      type TEXT NOT NULL,
      file_url TEXT NOT NULL,
      preview_url TEXT,
      file_size INTEGER,
      format TEXT,
      language TEXT DEFAULT 'sq',
      download_count INTEGER DEFAULT 0,
      disclaimer TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create user_activities table
  database.exec(`
    CREATE TABLE IF NOT EXISTS user_activities (
      id TEXT PRIMARY KEY,
      session_id TEXT,
      user_email TEXT,
      ip_address TEXT,
      activity_type TEXT NOT NULL,
      resource_id TEXT,
      inquiry_id TEXT,
      metadata TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (resource_id) REFERENCES resources(id),
      FOREIGN KEY (inquiry_id) REFERENCES inquiries(id)
    )
  `);

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_inquiries_status ON inquiries(status);
    CREATE INDEX IF NOT EXISTS idx_inquiries_category ON inquiries(inquiry_category);
    CREATE INDEX IF NOT EXISTS idx_inquiries_created_at ON inquiries(created_at);
    CREATE INDEX IF NOT EXISTS idx_resources_category ON resources(category);
    CREATE INDEX IF NOT EXISTS idx_resources_type ON resources(type);
    CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
    CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);
  `);
}

/**
 * Convert database row to Inquiry object
 */
export function rowToInquiry(row: InquiryRow): Inquiry {
  return {
    id: row.id,
    contactName: row.contact_name,
    contactEmail: row.contact_email,
    companyName: row.company_name || undefined,
    inquiryCategory: row.inquiry_category as Inquiry['inquiryCategory'],
    inquirySubcategory: row.inquiry_subcategory || undefined,
    urgencyLevel: row.urgency_level as Inquiry['urgencyLevel'],
    description: row.description,
    status: row.status as Inquiry['status'],
    assignedTo: row.assigned_to || undefined,
    requiresProfessional: Boolean(row.requires_professional),
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at)
  };
}

/**
 * Convert Inquiry object to database row
 */
export function inquiryToRow(inquiry: Inquiry): Omit<InquiryRow, 'created_at' | 'updated_at'> {
  return {
    id: inquiry.id,
    contact_name: inquiry.contactName,
    contact_email: inquiry.contactEmail,
    company_name: inquiry.companyName || undefined,
    inquiry_category: inquiry.inquiryCategory,
    inquiry_subcategory: inquiry.inquirySubcategory || undefined,
    urgency_level: inquiry.urgencyLevel,
    description: inquiry.description,
    status: inquiry.status,
    assigned_to: inquiry.assignedTo || undefined,
    requires_professional: inquiry.requiresProfessional ? 1 : 0
  };
}

/**
 * Convert database row to Resource object
 */
export function rowToResource(row: ResourceRow): Resource {
  return {
    id: row.id,
    title: row.title,
    description: row.description || undefined,
    category: row.category as Resource['category'],
    type: row.type as Resource['type'],
    fileUrl: row.file_url,
    previewUrl: row.preview_url || undefined,
    fileSize: row.file_size || undefined,
    format: row.format || undefined,
    language: row.language as Resource['language'],
    downloadCount: row.download_count,
    disclaimer: row.disclaimer || undefined,
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at)
  };
}

/**
 * Convert Resource object to database row
 */
export function resourceToRow(resource: Resource): Omit<ResourceRow, 'created_at' | 'updated_at'> {
  return {
    id: resource.id,
    title: resource.title,
    description: resource.description || undefined,
    category: resource.category,
    type: resource.type,
    file_url: resource.fileUrl,
    preview_url: resource.previewUrl || undefined,
    file_size: resource.fileSize || undefined,
    format: resource.format || undefined,
    language: resource.language,
    download_count: resource.downloadCount,
    disclaimer: resource.disclaimer || undefined
  };
}

/**
 * Convert database row to UserActivity object
 */
export function rowToUserActivity(row: UserActivityRow): UserActivity {
  return {
    id: row.id,
    sessionId: row.session_id || undefined,
    userEmail: row.user_email || undefined,
    ipAddress: row.ip_address || undefined,
    activityType: row.activity_type as UserActivity['activityType'],
    resourceId: row.resource_id || undefined,
    inquiryId: row.inquiry_id || undefined,
    metadata: row.metadata || undefined,
    timestamp: new Date(row.timestamp)
  };
}

/**
 * Convert UserActivity object to database row
 */
export function userActivityToRow(activity: UserActivity): Omit<UserActivityRow, 'timestamp'> {
  return {
    id: activity.id,
    session_id: activity.sessionId || undefined,
    user_email: activity.userEmail || undefined,
    ip_address: activity.ipAddress || undefined,
    activity_type: activity.activityType,
    resource_id: activity.resourceId || undefined,
    inquiry_id: activity.inquiryId || undefined,
    metadata: activity.metadata || undefined
  };
}

/**
 * Generate UUID for database records
 */
export function generateId(): string {
  return crypto.randomUUID();
}