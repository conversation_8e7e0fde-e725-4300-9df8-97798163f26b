import React, { useState, useEffect } from 'react';
import { Mail, Phone, MapPin, Send, CheckCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next'; // Import the hook
import Hero from '../components/Hero';
import { useSEO, generateStructuredData } from '../contexts/SEOContext';
import { useFormSubmission } from '../hooks';
import { contactApi } from '../api/client';
import { processContactForm } from '../lib/validators';
import type { ContactFormData } from '../types';

const ContactPage: React.FC = () => {
  // Initialize the translation hook, specifying the 'contact' namespace
  const { t, i18n } = useTranslation(['contact', 'services']);
  const currentLang = i18n.language;
  const { updateSEO } = useSEO();

  // Update SEO for contact page
  useEffect(() => {
    const contactSchema = generateStructuredData('ContactPage', {
      name: currentLang === 'sq' ? 'Kontakto SafeWork Kosova' : 'Contact SafeWork Kosova',
      description: currentLang === 'sq'
        ? 'Kontaktoni SafeWork Kosova për shërbime profesionale të sigurisë në punë. Jemi këtu për t\'ju ndihmuar me nevojat tuaja të sigurisë.'
        : 'Contact SafeWork Kosova for professional workplace safety services. We are here to help with your safety needs.',
      mainEntity: {
        '@type': 'LocalBusiness',
        name: 'SafeWork Kosova',
        telephone: '+383-44-819-701',
  email: '<EMAIL>',
        address: {
          '@type': 'PostalAddress',
          streetAddress: 'Fatmir Ratkoceri Street',
          addressLocality: 'Ferizaj',
          postalCode: '70000',
          addressCountry: 'XK'
        },
        openingHours: 'Mo-Fr 09:00-17:00',
        priceRange: '$$'
      }
    });

    const organizationSchema = generateStructuredData('Organization', {
      name: 'SafeWork Kosova',
      contactPoint: [
        {
          '@type': 'ContactPoint',
          telephone: '+383-44-819-701',
          contactType: 'customer service',
          email: '<EMAIL>',
          availableLanguage: ['Albanian', 'English'],
          hoursAvailable: {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
            opens: '09:00',
            closes: '17:00'
          }
        }
      ]
    });

    updateSEO({
      title: currentLang === 'sq'
        ? 'Kontakto SafeWork Kosova - Shërbime Sigurie në Punë'
        : 'Contact SafeWork Kosova - Workplace Safety Services',
      description: currentLang === 'sq'
  ? 'Kontaktoni SafeWork Kosova për konsulencë profesionale të sigurisë në punë. Telefon: +383-44-819-701. Email: <EMAIL>. Jemi në Ferizaj, Kosovë.'
  : 'Contact SafeWork Kosova for professional workplace safety consulting. Phone: +383-44-819-701. Email: <EMAIL>. Located in Ferizaj, Kosovo.',
      keywords: currentLang === 'sq'
        ? ['kontakt SafeWork', 'telefon sigurie', 'email sigurie', 'adresë SafeWork', 'Ferizaj', 'konsulencë sigurie']
        : ['contact SafeWork', 'safety phone', 'safety email', 'SafeWork address', 'Ferizaj', 'safety consulting'],
      type: 'contact',
      structuredData: [contactSchema, organizationSchema],
      openGraph: {
        title: currentLang === 'sq'
          ? 'Kontakto SafeWork Kosova për Shërbime Sigurie'
          : 'Contact SafeWork Kosova for Safety Services',
        description: currentLang === 'sq'
          ? 'Jemi këtu për t\'ju ndihmuar me nevojat tuaja të sigurisë në punë. Kontaktoni ekspertët tanë sot.'
          : 'We are here to help with your workplace safety needs. Contact our experts today.',
        type: 'website'
      }
    });
  }, [currentLang, updateSEO]);

  const [formState, setFormState] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    service: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { submit, loading } = useFormSubmission(contactApi.submit);
  const [showSuccess, setShowSuccess] = useState(false);

  // Form validation is now handled by the processContactForm utility

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Use new validation system
    const { sanitizedData, errors: validationErrors, isValid } = processContactForm(formState);

    if (!isValid) {
      setErrors(validationErrors);
      return;
    }

    setErrors({}); // Clear any previous errors

  try {
    // --- THE FIX IS HERE ---
    // Create a new object that includes the sanitized form data AND the current language
    const dataWithLanguage = {
      ...sanitizedData,
      language: currentLang
    };

    // Pass the complete data object (including language) to the submission function
    const response = await submit(dataWithLanguage);

    if (response.success) {
      setShowSuccess(true);
      
        // Reset form on successful submission
        setFormState({
          name: '',
          email: '',
          phone: '',
          company: '',
          message: '',
          service: '',
        });
      } else {
        // Handle API errors
        if (response.details) {
          const serverErrors: Record<string, string> = {};
          response.details.forEach((detail: string) => {
            if (detail.includes('Name')) serverErrors.name = detail;
            else if (detail.includes('Email') || detail.includes('email')) serverErrors.email = detail;
            else if (detail.includes('Phone')) serverErrors.phone = detail;
            else if (detail.includes('Company')) serverErrors.company = detail;
            else if (detail.includes('Message')) serverErrors.message = detail;
            else if (detail.includes('Service')) serverErrors.service = detail;
          });
          setErrors(serverErrors);
        } else {
          setErrors({ general: response.error || t('error_submission_failed') });
        }
      }
    } catch (networkError) {
      console.error('Network/server error:', networkError);
      setErrors({ general: t('error_network') });
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title={t('hero_title')}
        subtitle={t('hero_subtitle')}
        ctaText={t('hero_cta')}
        ctaLink="#formular-kontakti"
        bgImage="/images/contact-hero.jpg"
      />

      {/* Contact Info Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">{t('info_title')}</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('info_subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600 text-center shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-block p-3 bg-blue-100 dark:bg-blue-900 rounded-full mb-4"><Phone className="w-6 h-6 text-blue-800 dark:text-blue-300" /></div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('info_call_title')}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm">{t('info_call_subtitle')}</p>
              <a href="tel:+38344123456" className="text-blue-800 dark:text-blue-300 font-medium hover:text-orange-500 dark:hover:text-orange-400 transition-colors text-lg">+383.44.819.701</a>
            </div>
            <div className="bg-white dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600 text-center shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-block p-3 bg-blue-100 dark:bg-blue-900 rounded-full mb-4"><Mail className="w-6 h-6 text-blue-800 dark:text-blue-300" /></div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('info_email_title')}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm">{t('info_email_subtitle')}</p>
              <a href="mailto:<EMAIL>" className="text-blue-800 dark:text-blue-300 font-medium hover:text-orange-500 dark:hover:text-orange-400 transition-colors break-all"><EMAIL></a>
            </div>
            <div className="bg-white dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600 text-center shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-block p-3 bg-blue-100 dark:bg-blue-900 rounded-full mb-4"><MapPin className="w-6 h-6 text-blue-800 dark:text-blue-300" /></div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('info_visit_title')}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm">{t('info_visit_subtitle')}</p>
              <address className="text-gray-700 dark:text-gray-300 font-medium not-italic leading-snug whitespace-pre-line">{t('info_address')}</address>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="formular-kontakti" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="text-center mb-10">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">{t('form_title')}</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">{t('form_subtitle')}</p>
            </div>
            {showSuccess ? (
              <div className="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 dark:border-green-500 rounded-r-lg p-6 text-center shadow-md">
                <div className="flex justify-center mb-4"><div className="inline-block p-3 bg-green-100 dark:bg-green-800 rounded-full"><CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" /></div></div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('form_success_title')}</h3>
                <p className="text-gray-700 dark:text-gray-300">{t('form_success_message')}</p>
                <button
                  className="mt-6 px-6 py-2 bg-green-600 dark:bg-green-700 text-white rounded shadow hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 dark:focus:ring-green-500"
                  onClick={() => {
                    setShowSuccess(false);
                    setIsSubmitted(false);
                    setFormState({ name: '', email: '', phone: '', company: '', message: '', service: '' });
                  }}
                >
                  {t('form_success_close')}
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} noValidate>
                {errors.general && (
                  <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_name')}</label>
                    <input type="text" id="name" name="name" value={formState.name} onChange={handleChange} className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${errors.name ? 'border-red-500 ring-red-200 dark:border-red-400 dark:ring-red-800' : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800'}`} placeholder={t('placeholder_name')} required />
                    {errors.name && (<p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.name}</p>)}
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_email')}</label>
                    <input type="email" id="email" name="email" value={formState.email} onChange={handleChange} className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${errors.email ? 'border-red-500 ring-red-200 dark:border-red-400 dark:ring-red-800' : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800'}`} placeholder={t('placeholder_email')} required />
                    {errors.email && (<p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.email}</p>)}
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_phone')}</label>
                    <input type="tel" id="phone" name="phone" value={formState.phone} onChange={handleChange} className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder={t('placeholder_phone')} />
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_company')}</label>
                    <input type="text" id="company" name="company" value={formState.company} onChange={handleChange} className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder={t('placeholder_company')} />
                  </div>
                  <div className="md:col-span-2">
                    <label htmlFor="service" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_service')}</label>
                    <select id="service" name="service" value={formState.service} onChange={handleChange} className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${errors.service ? 'border-red-500 ring-red-200 dark:border-red-400 dark:ring-red-800' : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800'}`} required>
                      <option value="" disabled>{t('placeholder_service')}</option>
                      <option value="training">{t('services:training.title')}</option>
                      <option value="consulting">{t('services:consulting.title')}</option>
                      <option value="audits">{t('services:audits.title')}</option>
                      <option value="compliance">{t('services:compliance.title')}</option>
                      <option value="reporting">{t('services:reporting.title')}</option>
                      <option value="emergency">{t('services:emergency.title')}</option>
                      <option value="mining-safety">{t('services:mining-safety.title')}</option>
                      <option value="sds">{t('services:sds.title')}</option>
                      <option value="other">{t('service_other')}</option>
                    </select>
                    {errors.service && (<p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.service}</p>)}
                  </div>
                  <div className="md:col-span-2">
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('label_message')}</label>
                    <textarea id="message" name="message" value={formState.message} onChange={handleChange} rows={5} className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${errors.message ? 'border-red-500 ring-red-200 dark:border-red-400 dark:ring-red-800' : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-200 dark:focus:ring-blue-800'}`} placeholder={t('placeholder_message')} required></textarea>
                    {errors.message && (<p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.message}</p>)}
                  </div>
                </div>
                <div className="text-center mt-8">
                  <button type="submit" className="btn-primary py-3 px-8 inline-flex items-center justify-center w-full sm:w-auto disabled:opacity-60 disabled:cursor-not-allowed" disabled={loading}>
                    {loading ? (
                      <><svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>{t('button_submitting')}</>
                    ) : (
                      <><Send className="mr-2 h-5 w-5" />{t('button_submit')}</>
                    )}
                  </button>
                  <p className="mt-4 text-xs text-gray-500 dark:text-gray-400">{t('form_note')}</p>
                </div>
              </form>
            )}
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="pb-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">{t('map_title')}</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{t('map_subtitle')}</p>
          </div>
          <div className="rounded-lg overflow-hidden h-96 relative shadow-md">
            <iframe src="https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d1078.1857007415276!2d21.150274440165482!3d42.38114117558183!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zNDLCsDIyJzUyLjEiTiAyMcKwMDknMDMuNyJF!5e1!3m2!1sen!2s!4v1746391598141!5m2!1sen!2s" width="100%" height="100%" style={{ border: 0 }} allowFullScreen={false} loading="lazy" referrerPolicy="no-referrer-when-downgrade" title="Lokacioni i Zyrës"></iframe>
          </div>
          <div className="mt-8 max-w-md mx-auto">
             <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 text-center">
              <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2">{t('map_office_title')}</h3>
              <address className="text-gray-600 dark:text-gray-300 not-italic mb-3 leading-relaxed whitespace-pre-line">{t('info_address')}</address>
              <p className="text-gray-600 dark:text-gray-300"><span className="font-medium">{t('map_office_tel')}</span><a href="tel:+38344123456" className="ml-1 text-blue-700 dark:text-blue-400 hover:text-orange-600 dark:hover:text-orange-400">+383.44.819.701</a></p>
               <p className="text-gray-600 dark:text-gray-300 mt-1"><span className="font-medium">{t('map_office_email')}</span><a href="mailto:<EMAIL>" className="ml-1 text-blue-700 dark:text-blue-400 hover:text-orange-600 dark:hover:text-orange-400"><EMAIL></a></p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
