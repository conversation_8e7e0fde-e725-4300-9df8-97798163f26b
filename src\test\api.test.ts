import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getDatabase, closeDatabase } from '../lib/database.js';

// Mock Resend for testing
vi.mock('resend', () => ({
  Resend: vi.fn().mockImplementation(() => ({
    emails: {
      send: vi.fn().mockResolvedValue({ error: null })
    }
  }))
}));

// Mock environment variables
vi.mock('process', () => ({
  env: {
    RESEND_API_KEY: 'test-api-key',
    EMAIL_RECIPIENTS: '<EMAIL>',
    ADMIN_API_TOKEN: 'test-admin-token'
  }
}));

describe('API Endpoints', () => {
  beforeEach(() => {
    // Initialize database for each test
    const db = getDatabase();
    // Clear all tables
    db.exec('DELETE FROM inquiries');
    db.exec('DELETE FROM user_activities');
  });

  afterEach(() => {
    closeDatabase();
    vi.clearAllMocks();
  });

  describe('POST /api/users/inquiry', () => {
    // Mock request and response objects
    const createMockReq = (body: any, headers: any = {}) => ({
      method: 'POST',
      body,
      headers: {
        'user-agent': 'test-agent',
        'x-forwarded-for': '127.0.0.1',
        ...headers
      },
      connection: { remoteAddress: '127.0.0.1' }
    });

    const createMockRes = () => {
      const res: any = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis()
      };
      return res;
    };

    it('should create inquiry with valid data', async () => {
      const { default: handler } = await import('../../api/users/inquiry.js');
      
      const validData = {
        contactName: 'John Doe',
        contactEmail: '<EMAIL>',
        companyName: 'Test Company',
        inquiryCategory: 'risk-assessment',
        inquirySubcategory: 'workplace hazards',
        urgencyLevel: 'medium',
        description: 'Need help with workplace risk assessment',
        requiresProfessional: true,
        language: 'en'
      };

      const req = createMockReq(validData);
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: expect.stringContaining('successfully'),
          data: expect.objectContaining({
            id: expect.any(String),
            status: 'new',
            category: 'risk-assessment',
            urgencyLevel: 'medium'
          })
        })
      );
    });

    it('should reject invalid data', async () => {
      const { default: handler } = await import('../../api/users/inquiry.js');
      
      const invalidData = {
        contactName: 'A', // Too short
        contactEmail: 'invalid-email',
        inquiryCategory: 'invalid-category',
        urgencyLevel: 'invalid-level',
        description: 'Short' // Too short
      };

      const req = createMockReq(invalidData);
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: expect.arrayContaining([
            expect.objectContaining({ field: 'contactName' }),
            expect.objectContaining({ field: 'contactEmail' }),
            expect.objectContaining({ field: 'inquiryCategory' }),
            expect.objectContaining({ field: 'urgencyLevel' }),
            expect.objectContaining({ field: 'description' })
          ])
        })
      );
    });

    it('should handle OPTIONS request', async () => {
      const { default: handler } = await import('../../api/users/inquiry.js');
      
      const req = { method: 'OPTIONS' };
      const res = createMockRes();

      await handler(req, res);

      expect(res.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', '*');
      expect(res.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'POST, OPTIONS');
      expect(res.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Headers', 'Content-Type');
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.end).toHaveBeenCalled();
    });

    it('should reject non-POST methods', async () => {
      const { default: handler } = await import('../../api/users/inquiry.js');
      
      const req = { method: 'GET' };
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Method not allowed'
        })
      );
    });

    it('should sanitize input data', async () => {
      const { default: handler } = await import('../../api/users/inquiry.js');
      
      const dataWithHtml = {
        contactName: '  John <script>alert("xss")</script> Doe  ',
        contactEmail: '  <EMAIL>  ',
        companyName: 'Test <b>Company</b>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'medium',
        description: 'Need help with <script>dangerous</script> assessment',
        language: 'en'
      };

      const req = createMockReq(dataWithHtml);
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      
      // Verify the inquiry was created with sanitized data
      const db = getDatabase();
      const inquiry = db.prepare('SELECT * FROM inquiries ORDER BY created_at DESC LIMIT 1').get();
      
      expect(inquiry.contact_name).toBe('John scriptalert("xss")/script Doe');
      expect(inquiry.contact_email).toBe('<EMAIL>');
      expect(inquiry.company_name).toBe('Test bCompany/b');
      expect(inquiry.description).toBe('Need help with scriptdangerous/script assessment');
    });
  });

  describe('GET /api/admin/inquiries', () => {
    const createMockReq = (query: any = {}, headers: any = {}) => ({
      method: 'GET',
      query,
      headers: {
        'authorization': 'Bearer test-admin-token',
        'x-forwarded-for': '127.0.0.1',
        ...headers
      },
      connection: { remoteAddress: '127.0.0.1' }
    });

    const createMockRes = () => {
      const res: any = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis()
      };
      return res;
    };

    beforeEach(async () => {
      // Create test inquiries
      const { createInquiry } = await import('../lib/inquiryService.js');
      
      createInquiry({
        contactName: 'User 1',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'risk-assessment',
        urgencyLevel: 'high',
        description: 'High priority risk assessment'
      });

      createInquiry({
        contactName: 'User 2',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'training',
        urgencyLevel: 'medium',
        description: 'Training request'
      });
    });

    it('should return all inquiries for authenticated admin', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq();
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.arrayContaining([
            expect.objectContaining({
              contactName: 'User 1',
              inquiryCategory: 'risk-assessment'
            }),
            expect.objectContaining({
              contactName: 'User 2',
              inquiryCategory: 'training'
            })
          ]),
          pagination: expect.objectContaining({
            limit: 50,
            offset: 0,
            total: 2
          })
        })
      );
    });

    it('should filter inquiries by category', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq({ category: 'risk-assessment' });
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.arrayContaining([
            expect.objectContaining({
              inquiryCategory: 'risk-assessment'
            })
          ])
        })
      );

      const responseData = res.json.mock.calls[0][0];
      expect(responseData.data).toHaveLength(1);
    });

    it('should return statistics when requested', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq({ stats: 'true' });
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            total: 2,
            byStatus: expect.objectContaining({
              new: 2
            }),
            byCategory: expect.objectContaining({
              'risk-assessment': 1,
              'training': 1
            }),
            byUrgency: expect.objectContaining({
              high: 1,
              medium: 1
            })
          })
        })
      );
    });

    it('should reject unauthenticated requests', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq({}, { authorization: 'Bearer invalid-token' });
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Unauthorized access',
          code: 'UNAUTHORIZED'
        })
      );
    });

    it('should reject requests without authorization header', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq({}, {});
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
    });

    it('should validate filter parameters', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq({ 
        category: 'invalid-category',
        urgencyLevel: 'invalid-level',
        limit: '0'
      });
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid filters',
          code: 'VALIDATION_ERROR'
        })
      );
    });
  });

  describe('PUT /api/admin/inquiries', () => {
    let testInquiryId: string;

    const createMockReq = (query: any = {}, body: any = {}, headers: any = {}) => ({
      method: 'PUT',
      query,
      body,
      headers: {
        'authorization': 'Bearer test-admin-token',
        'x-forwarded-for': '127.0.0.1',
        ...headers
      },
      connection: { remoteAddress: '127.0.0.1' }
    });

    const createMockRes = () => {
      const res: any = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis()
      };
      return res;
    };

    beforeEach(async () => {
      // Create test inquiry
      const { createInquiry } = await import('../lib/inquiryService.js');
      
      const inquiry = createInquiry({
        contactName: 'Test User',
        contactEmail: '<EMAIL>',
        inquiryCategory: 'general',
        urgencyLevel: 'medium',
        description: 'Test inquiry for updates'
      });

      testInquiryId = inquiry.id;
    });

    it('should update inquiry status', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq(
        { id: testInquiryId },
        { status: 'in-progress' }
      );
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: 'Inquiry updated successfully',
          data: expect.objectContaining({
            id: testInquiryId,
            status: 'in-progress'
          })
        })
      );
    });

    it('should update assigned user', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq(
        { id: testInquiryId },
        { assignedTo: '<EMAIL>' }
      );
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            assignedTo: '<EMAIL>'
          })
        })
      );
    });

    it('should reject invalid status values', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq(
        { id: testInquiryId },
        { status: 'invalid-status' }
      );
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid status value',
          code: 'VALIDATION_ERROR'
        })
      );
    });

    it('should return 404 for non-existent inquiry', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq(
        { id: 'non-existent-id' },
        { status: 'closed' }
      );
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Inquiry not found',
          code: 'NOT_FOUND'
        })
      );
    });

    it('should require inquiry ID', async () => {
      const { default: handler } = await import('../../api/admin/inquiries.js');
      
      const req = createMockReq(
        {}, // No ID in query
        { status: 'closed' }
      );
      const res = createMockRes();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Inquiry ID is required',
          code: 'MISSING_ID'
        })
      );
    });
  });
});