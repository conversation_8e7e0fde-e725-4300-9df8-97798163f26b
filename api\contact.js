// Vercel Serverless Function for Contact Form
import { Resend } from 'resend';

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Email validation function
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

// HTML escape function for security
function escapeHtml(text) {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, function (m) { return map[m]; });
}

// Get email recipients
function getEmailRecipients() {
  const recipients = process.env.EMAIL_RECIPIENTS || '<EMAIL>';
  return recipients.split(',').map(email => email.trim());
}

// Service translation mapping
function getServiceTranslation(serviceKey, language) {
  const serviceTranslations = {
    'training': {
      'en': 'Safety Training & Education',
      'sq': 'Trajnime dhe Edukime për Sigurinë'
    },
    'consulting': {
      'en': 'Safety Consulting & Advisory',
      'sq': 'Konsulencë dhe Këshillime për Sigurinë'
    },
    'audits': {
      'en': 'Safety Audits & Inspections',
      'sq': 'Auditime dhe Inspektime të Sigurisë'
    },
    'compliance': {
      'en': 'Regulatory Compliance',
      'sq': 'Përputhshmëria me Rregulloret'
    },
    'reporting': {
      'en': 'Incident Reporting & Investigation',
      'sq': 'Raportimi dhe Hetimi i Incidenteve'
    },
    'emergency': {
      'en': 'Emergency Response Planning',
      'sq': 'Planifikimi i Përgjigjes së Emergjencës'
    },
    'mining-safety': {
      'en': 'Mining Safety Services',
      'sq': 'Shërbimet e Sigurisë në Miniera'
    },
    'sds': {
      'en': 'Safety Data Sheets (SDS)',
      'sq': 'Fletët e të Dhënave të Sigurisë (SDS)'
    },
    'other': {
      'en': 'Other Services',
      'sq': 'Shërbime të Tjera'
    }
  };

  return serviceTranslations[serviceKey]?.[language] || serviceKey;
}

// Rate limiting storage (in-memory for simplicity)
const rateLimitStore = new Map();

function checkRateLimit(ip, limit = 5, windowMs = 60 * 60 * 1000) {
  const now = Date.now();
  const key = `contact_${ip}`;

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const record = rateLimitStore.get(key);

  if (now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Rate limiting
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
    if (!checkRateLimit(clientIP)) {
      return res.status(429).json({
        error: 'Too many contact form submissions, please try again later.'
      });
    }

    const { name, email, phone, company, message, service, language } = req.body;

    // Validation
    const errors = [];

    if (!name || name.trim().length < 2 || name.trim().length > 100) {
      errors.push('Name must be between 2 and 100 characters');
    }

    if (!email || !isValidEmail(email)) {
      errors.push('Please provide a valid email address');
    }

    if (!message || message.trim().length < 10) {
      errors.push('Message must be at least 10 characters long');
    }

    if (!service) {
      errors.push('Please select a service');
    }

    if (errors.length > 0) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors
      });
    }

    // Check if Resend API key is configured
    if (!process.env.RESEND_API_KEY) {
      console.error('Resend API key not configured');
      return res.status(500).json({ error: 'Email service not configured' });
    }

    const recipients = getEmailRecipients();

    // Get translated service name
    const translatedService = getServiceTranslation(service, language || 'en');

    // Escape all user input for safe HTML rendering
    const safeData = {
      name: escapeHtml(name.trim()),
      email: escapeHtml(email.trim()),
      phone: escapeHtml((phone || 'Not provided').trim()),
      company: escapeHtml((company || 'Not provided').trim()),
      service: escapeHtml(service),
      translatedService: escapeHtml(translatedService),
      message: escapeHtml(message.trim()).replace(/\n/g, '<br>'),
      language: escapeHtml(language || 'Not specified')
    };

    // Send email using Resend
    const { error } = await resend.emails.send({
      from: 'Contact Form <<EMAIL>>',
      to: recipients,
      subject: `New Contact Form Submission - ${safeData.translatedService}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Contact Form Submission</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Header -->
            <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 30px 20px; text-align: center;">
              <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">SafeWork Kosova</h1>
              <p style="color: #dbeafe; margin: 10px 0 0 0; font-size: 16px;">New Contact Form Submission</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px 20px;">
              <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
                <p style="margin: 0; color: #92400e; font-weight: 600;">🔔 New inquiry received for: ${safeData.translatedService}</p>
              </div>
              
              <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 22px;">Contact Details</h2>
              
              <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px; background-color: #f9fafb; border-radius: 8px; overflow: hidden;">
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6; width: 30%;">👤 Name:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${safeData.name}</td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">📧 Email:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;"><a href="mailto:${safeData.email}" style="color: #2563eb; text-decoration: none;">${safeData.email}</a></td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">📞 Phone:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${safeData.phone}</td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">🏢 Company:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${safeData.company}</td>
                </tr>
                <tr>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; background-color: #f3f4f6;">🛡️ Service:</td>
                  <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; color: #1f2937;">
                    <span style="background-color: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500;">${safeData.translatedService}</span>
                    <br><small style="color: #6b7280; font-size: 12px;">Key: ${safeData.service}</small>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 15px; font-weight: 600; color: #374151; background-color: #f3f4f6; vertical-align: top;">💬 Message:</td>
                  <td style="padding: 15px; color: #1f2937; line-height: 1.6;">${safeData.message}</td>
                </tr>
              </table>
              
              <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; color: #0c4a6e; font-size: 16px;">📊 Submission Info</h3>
                <p style="margin: 0; color: #0c4a6e; font-size: 14px;">
                  <strong>Language:</strong> ${safeData.language}<br>
                  <strong>Submitted:</strong> ${new Date().toLocaleString()}<br>
                  <strong>IP Address:</strong> ${clientIP}
                </p>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
              <p style="margin: 0; color: #6b7280; font-size: 12px;">
                This is an automated notification from SafeWork Kosova contact form.<br>
                Please respond to the customer directly at their email address.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    });

    if (error) {
      console.error('Resend error:', error);
      return res.status(500).json({ error: 'Failed to send email. Please try again later.' });
    }

    // Prepare confirmation email content based on language
    let confirmationSubject = '';
    let confirmationHtmlBody = '';
    let responseMessage = '';

    switch (language) {
      case 'en':
        confirmationSubject = 'Thank you for your inquiry to SafeWork Kosova';
        confirmationHtmlBody = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Thank you for contacting SafeWork Kosova</title>
          </head>
          <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
              <!-- Header -->
              <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
                <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Your Partner in Safety Excellence</p>
              </div>
              
              <!-- Content -->
              <div style="padding: 40px 30px;">
                <div style="text-align: center; margin-bottom: 30px;">
                  <table style="margin: 0 auto 20px auto;">
                    <tr>
                      <td style="background-color: #10b981; color: white; width: 60px; height: 60px; border-radius: 30px; text-align: center; vertical-align: middle; font-size: 24px; font-weight: bold;">✓</td>
                    </tr>
                  </table>
                  <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Thank You, ${safeData.name}!</h2>
                  <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">We've received your inquiry and will respond soon</p>
                </div>
                
                <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
                  <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 Your Inquiry Summary</h3>
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="padding: 10px 0; font-weight: 600; color: #374151; width: 30%;">🛡️ Service:</td>
                      <td style="padding: 10px 0; color: #1f2937;">
                        <span style="background-color: #dbeafe; color: #1e40af; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.translatedService}</span>
                      </td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: 600; color: #374151; vertical-align: top;">💬 Message:</td>
                      <td style="padding: 10px 0; color: #1f2937; line-height: 1.6;">${safeData.message}</td>
                    </tr>
                  </table>
                </div>
                
                <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
                  <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">⏱️ What Happens Next?</h3>
                  <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li style="margin-bottom: 8px;">Our safety experts will review your inquiry</li>
                    <li style="margin-bottom: 8px;">We'll contact you soon</li>
                    <li style="margin-bottom: 0;">We'll provide a customized safety solution</li>
                  </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                  <p style="color: #6b7280; margin: 0 0 20px 0; font-size: 16px;">Explore our safety resources while you wait:</p>
                  <a href="https://safework-kosova.com/en/services" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Our Services</a>
                  <a href="https://safework-kosova.com/en/case-studies" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Case Studies</a>
                </div>
              </div>
              
              <!-- Contact Info -->
              <div style="background-color: #1f2937; padding: 30px 20px; text-align: center;">
                <h3 style="color: #ffffff; margin: 0 0 20px 0; font-size: 20px;">Contact Information</h3>
                <div style="display: inline-block; text-align: left;">
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📧 Email:</strong> 
                    <a href="mailto:<EMAIL>" style="color: #60a5fa; text-decoration: none;"><EMAIL></a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📞 Phone:</strong> 
                    <a href="tel:+38344819701" style="color: #60a5fa; text-decoration: none;">+383 44 819 701</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">🌐 Website:</strong> 
                    <a href="https://safework-kosova.com" style="color: #60a5fa; text-decoration: none;">www.safework-kosova.com</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📍 Address:</strong> Fatmir Ratkoceri Street, Ferizaj 70000, Kosovo
                  </p>
                </div>
              </div>
              
              <!-- Footer -->
              <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="margin: 0; color: #6b7280; font-size: 12px;">
                  This is an automated confirmation. Please do not reply to this email.<br>
                  If you have urgent questions, please call us directly at +383 44 819 701
                </p>
              </div>
            </div>
          </body>
          </html>
        `;
        responseMessage = 'Email sent successfully!';
        break;
      default: // Albanian
        confirmationSubject = 'Faleminderit për kërkesën tuaj drejtuar SafeWork Kosova';
        confirmationHtmlBody = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Faleminderit që kontaktuat SafeWork Kosova</title>
          </head>
          <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
              <!-- Header -->
              <div style="background-color: #1e40af; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); padding: 40px 20px; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">SafeWork Kosova</h1>
                <p style="color: #dbeafe; margin: 15px 0 0 0; font-size: 18px;">Partneri Juaj në Ekselencën e Sigurisë në Punë</p>
              </div>
              
              <!-- Content -->
              <div style="padding: 40px 30px;">
                <div style="text-align: center; margin-bottom: 30px;">
                  <table style="margin: 0 auto 20px auto;">
                    <tr>
                      <td style="background-color: #10b981; color: white; width: 60px; height: 60px; border-radius: 30px; text-align: center; vertical-align: middle; font-size: 24px; font-weight: bold;">✓</td>
                    </tr>
                  </table>
                  <h2 style="color: #1f2937; margin: 0; font-size: 28px;">Faleminderit, ${safeData.name}!</h2>
                  <p style="color: #6b7280; margin: 15px 0 0 0; font-size: 16px;">Kemi pranuar kërkesën tuaj dhe do t'ju përgjigjemi së shpejti</p>
                </div>
                
                <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
                  <h3 style="margin: 0 0 15px 0; color: #0c4a6e; font-size: 18px;">📋 Përmbledhja e Kërkesës Suaj</h3>
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="padding: 10px 0; font-weight: 600; color: #374151; width: 30%;">🛡️ Shërbimi:</td>
                      <td style="padding: 10px 0; color: #1f2937;">
                        <span style="background-color: #dbeafe; color: #1e40af; padding: 6px 12px; border-radius: 6px; font-weight: 500;">${safeData.translatedService}</span>
                      </td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: 600; color: #374151; vertical-align: top;">💬 Mesazhi:</td>
                      <td style="padding: 10px 0; color: #1f2937; line-height: 1.6;">${safeData.message}</td>
                    </tr>
                  </table>
                </div>
                
                <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 25px 0;">
                  <h3 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">⏱️ Çfarë Ndodh Tani?</h3>
                  <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li style="margin-bottom: 8px;">Ekspertët tanë të sigurisë në punë do ta shqyrtojnë kërkesën tuaj</li>
                    <li style="margin-bottom: 8px;">Do t'ju kontaktojmë së shpejti</li>
                    <li style="margin-bottom: 0;">Do t'ju ofrojmë një zgjidhje të personalizuar për nevojat tuaja</li>
                  </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                  <p style="color: #6b7280; margin: 0 0 20px 0; font-size: 16px;">Eksploroni burimet tona të sigurisë ndërsa prisni:</p>
                  <a href="https://safework-kosova.com/sq/services" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Shërbimet Tona</a>
                  <a href="https://safework-kosova.com/sq/case-studies" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 10px 10px 0;">Studimet e Rasteve</a>
                </div>
              </div>
              
              <!-- Contact Info -->
              <div style="background-color: #1f2937; padding: 30px 20px; text-align: center;">
                <h3 style="color: #ffffff; margin: 0 0 20px 0; font-size: 20px;">Informacionet e Kontaktit</h3>
                <div style="display: inline-block; text-align: left;">
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📧 Email:</strong> 
                    <a href="mailto:<EMAIL>" style="color: #60a5fa; text-decoration: none;"><EMAIL></a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📞 Telefoni:</strong> 
                    <a href="tel:+38344819701" style="color: #60a5fa; text-decoration: none;">+383 44 819 701</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">🌐 Faqja Web:</strong> 
                    <a href="https://safework-kosova.com" style="color: #60a5fa; text-decoration: none;">www.safework-kosova.com</a>
                  </p>
                  <p style="color: #d1d5db; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #ffffff;">📍 Adresa:</strong> Rruga Fatmir Ratkoceri, Ferizaj 70000, Kosovë
                  </p>
                </div>
              </div>
              
              <!-- Footer -->
              <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="margin: 0; color: #6b7280; font-size: 12px;">
                  Ky është një konfirmim i automatizuar.<br>
                  Nëse keni pyetje, na shkruani në: <a href="mailto:<EMAIL>" style="color: #0ea5e9; text-decoration: none;"><EMAIL></a>.
                </p>
              </div>
            </div>
          </body>
          </html>
        `;
        responseMessage = 'Mesazhi u dërgua me sukses!';
    }

    // Send confirmation email to user
    try {
      await resend.emails.send({
        from: 'SafeWork Kosova <<EMAIL>>',
        to: [safeData.email],
        subject: confirmationSubject,
        html: confirmationHtmlBody,
      });
    } catch (confirmationError) {
      console.error('⚠️ Failed to send confirmation email to user:', confirmationError);
      // Do not fail the request if confirmation email fails
    }

    res.status(200).json({ message: responseMessage });
  } catch (err) {
    console.error('Contact form error:', err);
    res.status(500).json({ error: 'An unexpected error occurred. Please try again later.' });
  }
}

