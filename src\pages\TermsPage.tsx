import React from 'react';
import { useTranslation } from 'react-i18next';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';

const TermsPage: React.FC = () => {
  const { t } = useTranslation('terms');

  return (
    <div>
      <Hero
        title={t('hero_title')}
        subtitle={t('hero_subtitle')}
        bgImage="/images/terms-hero.jpg"
      />

      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 md:px-6 max-w-4xl">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 md:p-12">
            <div className="mb-10 text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-3">{t('page_title')}</h2>
              <p className="text-blue-600 font-medium">{t('last_updated')}</p>
              <div className="w-20 h-1 bg-orange-500 mx-auto mt-6"></div>
            </div>

            <div className="space-y-8">
              <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500"><p className="text-gray-700 leading-relaxed">{t('intro_p1')}</p></div>
              
              {/* Sections 1-9 */}
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">1</span>{t('section1_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section1_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section1_p2')}</p><ol className="list-decimal pl-5 space-y-2 text-gray-700"><li>{t('section1_list_item1')}</li><li>{t('section1_list_item2')}</li><li>{t('section1_list_item3')}</li><li>{t('section1_list_item4')}</li></ol></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">2</span>{t('section2_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section2_p1')}</p><ol className="list-decimal pl-5 space-y-2 text-gray-700"><li>{t('section2_list_item1')}</li><li>{t('section2_list_item2')}</li><li>{t('section2_list_item3')}</li><li>{t('section2_list_item4')}</li></ol></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">3</span>{t('section3_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section3_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section3_p2')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">4</span>{t('section4_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section4_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section4_p2')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">5</span>{t('section5_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section5_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section5_p2')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">6</span>{t('section6_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section6_p1')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">7</span>{t('section7_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section7_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section7_p2')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">8</span>{t('section8_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section8_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section8_p2')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">9</span>{t('section9_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section9_p1')}</p><ul className="space-y-3 text-gray-700"><li><span>{t('section9_contact_email')} <EMAIL></span></li><li><span>{t('section9_contact_phone')} +383.44.819.701</span></li><li><span>{t('section9_contact_address')} Rr. Agim Ramadani, Nr. 23, 10000 Prishtinë, Kosovë</span></li></ul></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">9</span>{t('section9_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section9_p1')}</p><ul className="space-y-3 text-gray-700"><li><span>{t('section9_contact_email')} <EMAIL></span></li><li><span>{t('section9_contact_phone')} +383.44.819.701</span></li><li><span>{t('section9_contact_address')} Rr. Agim Ramadani, Nr. 23, 10000 Prishtinë, Kosovë</span></li></ul></div></div>
            </div>

            <div className="mt-12 text-center">
              <p className="text-gray-600 italic">{t('footer_p1')}</p>
            </div>
          </div>
        </div>
      </section>

      <CtaSection
        title={t('cta_title')}
        description={t('cta_subtitle')}
        ctaText={t('cta_button')}
        ctaLink="/contact"
      />
    </div>
  );
};

export default TermsPage;