import React, { useEffect } from 'react';
import { Outlet, useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const supportedLngs = ['sq', 'en'];

const LanguageLayout: React.FC = () => {
  const { lang } = useParams<{ lang: string }>();
  const { i18n } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    if (lang && supportedLngs.includes(lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang) {
      // If no language is in the URL, redirect to the detected or default language
      const detectedLng = i18n.language;
      navigate(`/${detectedLng}`, { replace: true });
    }
  }, [lang, i18n, navigate]);

  // Render a loading state or null while redirecting
  if (!lang || !supportedLngs.includes(lang)) {
    return null;
  }

  return <Outlet />;
};

export default LanguageLayout;