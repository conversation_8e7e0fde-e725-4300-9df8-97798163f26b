import React, { useEffect } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';

// Context providers
import { SEOProvider } from './contexts/SEOContext';

// Layout components
import MainLayout from './layouts/MainLayout';
import LanguageLayout from './layouts/LanguageLayout'; // Import the new layout

// Pages
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import ServicesPage from './pages/ServicesPage';
import ServiceDetailPage from './pages/ServiceDetailPage';
import CaseStudiesPage from './pages/CaseStudiesPage';
import CaseStudyDetailPage from './pages/CaseStudyDetailPage';
import BlogPage from './pages/BlogPage';
import BlogPostPage from './pages/BlogPostPage';
import ContactPage from './pages/ContactPage';
import PrivacyPage from './pages/PrivacyPage';
import TermsPage from './pages/TermsPage';
import NotFoundPage from './pages/NotFoundPage';

// ScrollToTop component to reset scroll position on navigation
function ScrollToTop() {
  const { pathname, hash } = useLocation();

  useEffect(() => {
    if (!hash) {
      window.scrollTo(0, 0);
    } else {
      setTimeout(() => {
        const element = document.getElementById(hash.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, [pathname, hash]);

  return null;
}

import { ThemeProvider } from './contexts/ThemeContext';
import { AccessibilityProvider, SkipLink } from './components/AccessibilityProvider';

function App() {
  return (
    <HelmetProvider>
      <ThemeProvider>
        <AccessibilityProvider>
          <Router>
            <SEOProvider>
              <SkipLink href="#main-content">Skip to main content</SkipLink>
              <SkipLink href="#navigation">Skip to navigation</SkipLink>
              <ScrollToTop />
              <Routes>
                {/* New language-aware parent route */}
                <Route path="/:lang" element={<LanguageLayout />}> 
                  {/* All existing routes are now nested here */}
                  <Route element={<MainLayout />}> 
                    <Route index element={<HomePage />} />
                    <Route path="about" element={<AboutPage />} />
                    <Route path="services" element={<ServicesPage />} />
                    <Route path="services/:serviceId" element={<ServiceDetailPage />} />
                    <Route path="case-studies" element={<CaseStudiesPage />} />
                    <Route path="case-studies/:caseStudyId" element={<CaseStudyDetailPage />} />
                    <Route path="blog" element={<BlogPage />} />
                    <Route path="blog/:postId" element={<BlogPostPage />} />
                    <Route path="contact" element={<ContactPage />} />
                    <Route path="privacy" element={<PrivacyPage />} />
                    <Route path="terms" element={<TermsPage />} />
                    <Route path="*" element={<NotFoundPage />} />
                  </Route>
                </Route>
                {/* A fallback route for the root path to trigger the redirect in LanguageLayout */}
                <Route path="/" element={<LanguageLayout />} />
              </Routes>
            </SEOProvider>
          </Router>
        </AccessibilityProvider>
      </ThemeProvider>
    </HelmetProvider>
  );
}

export default App;