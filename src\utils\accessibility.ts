/**
 * Accessibility utilities for WCAG 2.1 AA compliance
 * Includes contrast ratio calculations and validation
 */

// WCAG 2.1 contrast ratio requirements
export const WCAG_CONTRAST_RATIOS = {
  AA_NORMAL: 4.5,      // Normal text (18pt+ or 14pt+ bold)
  AA_LARGE: 3.0,       // Large text (18pt+ or 14pt+ bold)
  AAA_NORMAL: 7.0,     // Enhanced contrast for normal text
  AAA_LARGE: 4.5,      // Enhanced contrast for large text
} as const;

// Color conversion utilities
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

export const rgbStringToRgb = (rgb: string): { r: number; g: number; b: number } | null => {
  const match = rgb.match(/rgb\((\d+)\s*,?\s*(\d+)\s*,?\s*(\d+)\)/);
  if (match) {
    return {
      r: parseInt(match[1], 10),
      g: parseInt(match[2], 10),
      b: parseInt(match[3], 10)
    };
  }
  
  // Handle space-separated RGB values like "249 115 22"
  const spaceMatch = rgb.match(/(\d+)\s+(\d+)\s+(\d+)/);
  if (spaceMatch) {
    return {
      r: parseInt(spaceMatch[1], 10),
      g: parseInt(spaceMatch[2], 10),
      b: parseInt(spaceMatch[3], 10)
    };
  }
  
  return null;
};

// Calculate relative luminance according to WCAG 2.1
export const getRelativeLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

// Calculate contrast ratio between two colors
export const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = color1.startsWith('#') ? hexToRgb(color1) : rgbStringToRgb(color1);
  const rgb2 = color2.startsWith('#') ? hexToRgb(color2) : rgbStringToRgb(color2);
  
  if (!rgb1 || !rgb2) {
    console.warn('Invalid color format provided to getContrastRatio');
    return 0;
  }
  
  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

// Check if contrast ratio meets WCAG requirements
export const meetsContrastRequirement = (
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  const requirement = level === 'AA' 
    ? (isLargeText ? WCAG_CONTRAST_RATIOS.AA_LARGE : WCAG_CONTRAST_RATIOS.AA_NORMAL)
    : (isLargeText ? WCAG_CONTRAST_RATIOS.AAA_LARGE : WCAG_CONTRAST_RATIOS.AAA_NORMAL);
  
  return ratio >= requirement;
};

// Generate accessible color variations
export const generateAccessibleColor = (
  baseColor: string,
  backgroundColor: string,
  targetRatio: number = WCAG_CONTRAST_RATIOS.AA_NORMAL
): string => {
  const baseRgb = baseColor.startsWith('#') ? hexToRgb(baseColor) : rgbStringToRgb(baseColor);
  const bgRgb = backgroundColor.startsWith('#') ? hexToRgb(backgroundColor) : rgbStringToRgb(backgroundColor);
  
  if (!baseRgb || !bgRgb) {
    return baseColor;
  }
  
  // Try darkening or lightening the base color to meet contrast requirements
  let { r, g, b } = baseRgb;
  const bgLum = getRelativeLuminance(bgRgb.r, bgRgb.g, bgRgb.b);
  
  // Determine if we should darken or lighten
  const shouldDarken = bgLum > 0.5;
  const step = shouldDarken ? -5 : 5;
  
  for (let i = 0; i < 50; i++) {
    const currentRatio = getContrastRatio(
      `rgb(${r}, ${g}, ${b})`,
      backgroundColor
    );
    
    if (currentRatio >= targetRatio) {
      return `rgb(${r}, ${g}, ${b})`;
    }
    
    r = Math.max(0, Math.min(255, r + step));
    g = Math.max(0, Math.min(255, g + step));
    b = Math.max(0, Math.min(255, b + step));
  }
  
  return baseColor;
};

// Validate theme colors for accessibility
export interface ColorValidationResult {
  isValid: boolean;
  ratio: number;
  requirement: number;
  recommendation?: string;
}

export const validateColorCombination = (
  foreground: string,
  background: string,
  context: 'normal' | 'large' | 'interactive' = 'normal'
): ColorValidationResult => {
  const ratio = getContrastRatio(foreground, background);
  const isLargeText = context === 'large';
  const requirement = context === 'interactive' 
    ? WCAG_CONTRAST_RATIOS.AA_NORMAL // Interactive elements should meet normal text requirements
    : (isLargeText ? WCAG_CONTRAST_RATIOS.AA_LARGE : WCAG_CONTRAST_RATIOS.AA_NORMAL);
  
  const isValid = ratio >= requirement;
  
  let recommendation: string | undefined;
  if (!isValid) {
    if (ratio < WCAG_CONTRAST_RATIOS.AA_LARGE) {
      recommendation = 'Consider using a darker or lighter color to improve contrast';
    } else if (ratio < WCAG_CONTRAST_RATIOS.AA_NORMAL) {
      recommendation = 'This combination works for large text but not normal text';
    }
  }
  
  return {
    isValid,
    ratio,
    requirement,
    recommendation
  };
};

// Theme validation utility
export const validateThemeAccessibility = (theme: ColorPalette) => {
  const results: Record<string, ColorValidationResult> = {};
  
  // Validate primary text combinations
  results.primaryTextOnBackground = validateColorCombination(
    theme.text.primary,
    theme.background,
    'normal'
  );
  
  results.secondaryTextOnBackground = validateColorCombination(
    theme.text.secondary,
    theme.background,
    'normal'
  );
  
  results.mutedTextOnBackground = validateColorCombination(
    theme.text.muted,
    theme.background,
    'large' // Muted text is often smaller, but we'll be lenient
  );
  
  // Validate interactive elements
  results.primaryOnBackground = validateColorCombination(
    theme.primary,
    theme.background,
    'interactive'
  );
  
  results.secondaryOnBackground = validateColorCombination(
    theme.secondary,
    theme.background,
    'interactive'
  );
  
  results.accentOnBackground = validateColorCombination(
    theme.accent,
    theme.background,
    'interactive'
  );
  
  // Validate surface combinations
  results.primaryTextOnSurface = validateColorCombination(
    theme.text.primary,
    theme.surface,
    'normal'
  );
  
  return results;
};

// Focus management utilities
export const createFocusManager = () => {
  let lastFocusedElement: HTMLElement | null = null;
  
  return {
    saveFocus: () => {
      lastFocusedElement = document.activeElement as HTMLElement;
    },
    
    restoreFocus: () => {
      if (lastFocusedElement && typeof lastFocusedElement.focus === 'function') {
        lastFocusedElement.focus();
      }
    },
    
    trapFocus: (container: HTMLElement) => {
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
      
      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key !== 'Tab') return;
        
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      };
      
      container.addEventListener('keydown', handleTabKey);
      
      return () => {
        container.removeEventListener('keydown', handleTabKey);
      };
    }
  };
};

// Screen reader utilities
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Reduced motion utilities
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

export const createReducedMotionStyles = (normalStyles: Record<string, unknown>, reducedStyles: Record<string, unknown>) => {
  return prefersReducedMotion() ? reducedStyles : normalStyles;
};