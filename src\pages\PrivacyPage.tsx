import React from 'react';
import { useTranslation } from 'react-i18next';
import Hero from '../components/Hero';
import CtaSection from '../components/CtaSection';

const PrivacyPage: React.FC = () => {
  const { t } = useTranslation('privacy');

  return (
    <div>
      <Hero
        title={t('hero_title')}
        subtitle={t('hero_subtitle')}
        bgImage="/images/privacy-hero.jpg"
      />

      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 md:px-6 max-w-4xl">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 md:p-12">
            <div className="mb-10 text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-3">{t('page_title')}</h2>
              <p className="text-blue-600 font-medium">{t('last_updated')}</p>
              <div className="w-20 h-1 bg-orange-500 mx-auto mt-6"></div>
            </div>

            <div className="space-y-8">
              <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
                <p className="text-gray-700 leading-relaxed">{t('intro_p1')}</p>
              </div>

              {/* Section 1 */}
              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">1</span>
                  {t('section1_title')}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4 pl-11">{t('section1_p1')}</p>
                <div className="pl-11 mt-6">
                  <h4 className="text-xl font-medium text-gray-800 mb-3">{t('section1_subtitle1')}</h4>
                  <p className="text-gray-700 leading-relaxed mb-3">{t('section1_p2')}</p>
                  <ul className="list-disc pl-6 space-y-1 text-gray-700">
                    <li>{t('section1_list_item1')}</li>
                    <li>{t('section1_list_item2')}</li>
                    <li>{t('section1_list_item3')}</li>
                    <li>{t('section1_list_item4')}</li>
                    <li>{t('section1_list_item5')}</li>
                    <li>{t('section1_list_item6')}</li>
                  </ul>
                </div>
                <div className="pl-11 mt-6">
                  <h4 className="text-xl font-medium text-gray-800 mb-3">{t('section1_subtitle2')}</h4>
                  <p className="text-gray-700 leading-relaxed">{t('section1_p3')}</p>
                </div>
              </div>

              {/* Section 2 */}
              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">2</span>{t('section2_title')}</h3>
                <p className="text-gray-700 leading-relaxed mb-4 pl-11">{t('section2_p1')}</p>
                <ul className="pl-11 space-y-2 text-gray-700">
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item1')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item2')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item3')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item4')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item5')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item6')}</span></li>
                  <li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">✓</span><span>{t('section2_list_item7')}</span></li>
                </ul>
              </div>

              {/* Other Sections */}
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">3</span>{t('section3_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section3_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section3_p2')}</p><p className="text-gray-700 leading-relaxed">{t('section3_p3')}</p></div></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">4</span>{t('section4_title')}</h3><p className="text-gray-700 leading-relaxed mb-4 pl-11">{t('section4_p1')}</p><ul className="pl-11 space-y-2 text-gray-700"><li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">•</span><span>{t('section4_list_item1')}</span></li><li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">•</span><span>{t('section4_list_item2')}</span></li><li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">•</span><span>{t('section4_list_item3')}</span></li><li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">•</span><span>{t('section4_list_item4')}</span></li><li className="flex items-start"><span className="inline-block w-5 h-5 rounded-full bg-orange-100 text-orange-600 flex-shrink-0 flex items-center justify-center mr-2 mt-0.5">•</span><span>{t('section4_list_item5')}</span></li></ul></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">5</span>{t('section5_title')}</h3><p className="text-gray-700 leading-relaxed pl-11">{t('section5_p1')}</p></div>
              <div className="border-b border-gray-200 pb-6"><h3 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">6</span>{t('section6_title')}</h3><div className="pl-11 space-y-4"><p className="text-gray-700 leading-relaxed">{t('section6_p1')}</p><p className="text-gray-700 leading-relaxed">{t('section6_p2')}</p></div></div>
              
              {/* Contact Us Section */}
              <div className="mt-8"><h3 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center"><span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 inline-flex items-center justify-center mr-3">7</span>{t('section7_title')}</h3><div className="bg-gray-50 p-6 rounded-lg border border-gray-200"><p className="text-gray-700 leading-relaxed mb-4">{t('section7_p1')}</p><ul className="space-y-3 text-gray-700"><li className="flex items-center"><span>{t('section7_contact_email')} <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></span></li><li className="flex items-center"><span>{t('section7_contact_phone')} +383.44.819.701</span></li><li className="flex items-center"><span>{t('section7_contact_address')} Rruga Fatmir Ratkoceri, Ferizaj 70000, Kosovë</span></li></ul></div></div>
            </div>
          </div>
        </div>
      </section>

      <CtaSection
        title={t('cta_title')}
        description={t('cta_subtitle')}
        ctaText={t('cta_button')}
        ctaLink="/contact"
      />
    </div>
  );
};

export default PrivacyPage;