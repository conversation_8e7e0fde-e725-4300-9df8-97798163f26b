
# SafeWorkKosova

Professional workplace safety consulting website for SafeWork Kosova.

## Features
- Built with React, TypeScript, Vite, and Express.js
- Multilingual support (Albanian/English)
- Contact and newsletter forms (with Resend integration)
- SEO-optimized with Schema.org structured data
- Safety resources, blog, and case studies
- Responsive and accessible design

## Getting Started

### Prerequisites
- Node.js (v18 or newer recommended)
- npm or pnpm

### Install dependencies
```
npm install
# or
pnpm install
```

### Development
```
npm run dev
```
This runs both the Vite frontend and the Express backend concurrently.

### Build for Production
```
npm run build
```

### Environment Variables
Create a `.env` file with the following (see `.env.example` if available):
```
RESEND_API_KEY=your_resend_api_key
EMAIL_RECIPIENTS=<EMAIL>
```

## Deployment
Deployable to Vercel, Railway, or any Node.js-compatible host.

## License
MIT
npm run dev:both
