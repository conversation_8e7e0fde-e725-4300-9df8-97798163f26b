import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Shield, Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Clock } from 'lucide-react';
import { getServiceLinks } from '../constants/services';
import { SOCIAL_LINKS } from '../constants/common';

const Footer: React.FC = () => {
  const { t, i18n } = useTranslation(['common', 'services']);
  const currentLang = i18n.language;
  const currentYear = new Date().getFullYear();

  // Use shared services configuration (all services for footer)
  const services = getServiceLinks(currentLang);

  return (
    <footer className="bg-blue-900 dark:bg-gray-900 text-white dark:text-gray-100">
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <Shield className="w-8 h-8 text-orange-400" />
              <span className="ml-2 text-xl font-bold">SafeWork Kosova</span>
            </div>
            <p className="text-blue-100 dark:text-gray-300 mb-4">{t('common:footer_description')}</p>
            <div className="flex space-x-3">
              <a 
                href={SOCIAL_LINKS.facebook} 
                className="text-blue-200 dark:text-gray-400 hover:text-orange-400 dark:hover:text-orange-400 transition-colors"
                aria-label="Follow us on Facebook"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a 
                href={SOCIAL_LINKS.twitter} 
                className="text-blue-200 dark:text-gray-400 hover:text-orange-400 dark:hover:text-orange-400 transition-colors"
                aria-label="Follow us on Twitter"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a 
                href={SOCIAL_LINKS.linkedin} 
                className="text-blue-200 dark:text-gray-400 hover:text-orange-400 dark:hover:text-orange-400 transition-colors"
                aria-label="Follow us on LinkedIn"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-700 dark:border-gray-700 pb-2">{t('common:footer_quickLinks')}</h3>
            <ul className="space-y-2">
              <li><Link to={`/${currentLang}`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_home')}</Link></li>
              <li><Link to={`/${currentLang}/about`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_about')}</Link></li>
              <li><Link to={`/${currentLang}/services`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_services')}</Link></li>
              <li><Link to={`/${currentLang}/case-studies`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_case_studies')}</Link></li>
              <li><Link to={`/${currentLang}/blog`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_blog')}</Link></li>
              <li><Link to={`/${currentLang}/contact`} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:nav_contact')}</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-700 dark:border-gray-700 pb-2">{t('common:footer_ourServices')}</h3>
            <ul className="space-y-2">
              {services.map(service => (
                <li key={service.id}>
                  <Link to={service.link} className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors">
                    {t(`services:${service.id}.title`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-700 dark:border-gray-700 pb-2">{t('common:footer_contact')}</h3>
            <ul className="space-y-3">
              <li className="flex items-start"><MapPin className="w-5 h-5 text-orange-400 mr-2 mt-0.5" /><span className="text-blue-100 dark:text-gray-300">{t('common:footer_address_value')}</span></li>
              <li className="flex items-center"><Phone className="w-5 h-5 text-orange-400 mr-2" /><span className="text-blue-100 dark:text-gray-300">{t('common:header_phone')}</span></li>
              <li className="flex items-center"><Mail className="w-5 h-5 text-orange-400 mr-2" /><a href="mailto:<EMAIL>" className="text-blue-100 dark:text-gray-300 hover:text-orange-400 dark:hover:text-orange-400 transition-colors"><EMAIL></a></li>
              <li className="flex items-center"><Clock className="w-5 h-5 text-orange-400 mr-2" /><span className="text-blue-100 dark:text-gray-300">{t('common:footer_hours_value')}</span></li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-blue-800 dark:border-gray-700 text-center text-blue-300 dark:text-gray-400 text-sm">
          <p>{t('common:footer_copyright', { year: currentYear })}</p>
          <div className="mt-2 space-x-4">
            <Link to={`/${currentLang}/privacy`} className="hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:footer_privacy')}</Link>
            <Link to={`/${currentLang}/terms`} className="hover:text-orange-400 dark:hover:text-orange-400 transition-colors">{t('common:footer_terms')}</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;