import React, { useContext } from 'react';
import { ThemeContext, ThemeContextType, EffectiveTheme } from '../contexts/ThemeContext';

// Custom hook to use Theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Helper function to get theme-specific value
export const getThemeValue = <T,>(
  lightValue: T,
  darkValue: T,
  effectiveTheme: EffectiveTheme
): T => {
  return effectiveTheme === 'dark' ? darkValue : lightValue;
};

// Helper function to generate theme-aware className
export const getThemeClassName = (
  baseClass: string,
  lightClass?: string,
  darkClass?: string,
  effectiveTheme?: EffectiveTheme
): string => {
  if (!lightClass && !darkClass) return baseClass;

  const theme = effectiveTheme || (typeof document !== 'undefined' && document.documentElement.classList.contains('dark') ? 'dark' : 'light');
  const themeClass = theme === 'dark' ? darkClass : lightClass;

  return themeClass ? `${baseClass} ${themeClass}` : baseClass;
};

// Helper to detect system theme preference
export const getSystemTheme = (): EffectiveTheme => {
  if (typeof window === 'undefined') return 'light';

  try {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  } catch (error) {
    console.warn('Failed to detect system theme preference:', error);
    return 'light';
  }
};

// Enhanced useTheme hook with additional utilities
export const useThemeUtils = () => {
  const themeContext = useTheme();

  // Helper to get theme-specific values
  const getThemeValue = <T,>(lightValue: T, darkValue: T): T => {
    return themeContext.effectiveTheme === 'dark' ? darkValue : lightValue;
  };

  // Helper to generate conditional classes based on theme
  const themeClasses = (lightClasses: string, darkClasses: string): string => {
    return themeContext.effectiveTheme === 'dark' ? darkClasses : lightClasses;
  };

  // Helper to check if current theme matches a specific theme
  const isTheme = (targetTheme: EffectiveTheme): boolean => {
    return themeContext.effectiveTheme === targetTheme;
  };

  // Helper to get theme-aware CSS variables
  const getThemeVar = (variable: string): string => {
    if (typeof document === 'undefined') return '';
    return getComputedStyle(document.documentElement).getPropertyValue(variable);
  };

  // Helper to apply theme-aware styles
  const applyThemeStyles = (element: HTMLElement, lightStyles: Partial<CSSStyleDeclaration>, darkStyles: Partial<CSSStyleDeclaration>): void => {
    const styles = themeContext.effectiveTheme === 'dark' ? darkStyles : lightStyles;
    Object.assign(element.style, styles);
  };

  // Helper to create theme-aware event handlers
  const createThemeHandler = <T extends (...args: unknown[]) => unknown,>(
    lightHandler: T,
    darkHandler: T
  ): T => {
    return (themeContext.effectiveTheme === 'dark' ? darkHandler : lightHandler) as T;
  };

  // Helper to get system theme preference
  const getSystemPreference = (): EffectiveTheme => {
    return getSystemTheme();
  };

  // Helper to check if system theme is supported
  const isSystemThemeSupported = (): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      return window.matchMedia('(prefers-color-scheme: dark)').matches !== undefined;
    } catch {
      return false;
    }
  };

  return {
    ...themeContext,
    getThemeValue,
    themeClasses,
    isTheme,
    getThemeVar,
    applyThemeStyles,
    createThemeHandler,
    getSystemPreference,
    isSystemThemeSupported,
    isDark: themeContext.effectiveTheme === 'dark',
    isLight: themeContext.effectiveTheme === 'light',
  };
};

// Hook for theme-aware animations and transitions
export const useThemeTransition = () => {
  const { effectiveTheme } = useTheme();

  // Get transition duration from CSS custom property
  const getTransitionDuration = (): string => {
    if (typeof document === 'undefined') return '200ms';
    return getComputedStyle(document.documentElement).getPropertyValue('--transition-theme') || '200ms';
  };

  // Create transition styles
  const transitionStyles = {
    transition: `all ${getTransitionDuration()} ease-in-out`,
  };

  // Helper to create staggered animations for theme changes
  const createStaggeredTransition = (delay: number = 0): React.CSSProperties => ({
    ...transitionStyles,
    transitionDelay: `${delay}ms`,
  });

  return {
    effectiveTheme,
    transitionStyles,
    createStaggeredTransition,
    getTransitionDuration,
  };
};

// Hook for theme-aware media queries
export const useThemeMediaQuery = () => {
  const { effectiveTheme } = useTheme();
  const [matches, setMatches] = React.useState<Record<string, boolean>>({});

  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQueries = {
      mobile: '(max-width: 768px)',
      tablet: '(min-width: 769px) and (max-width: 1024px)',
      desktop: '(min-width: 1025px)',
      darkMode: '(prefers-color-scheme: dark)',
      lightMode: '(prefers-color-scheme: light)',
      reducedMotion: '(prefers-reduced-motion: reduce)',
    };

    const mediaQueryLists: Record<string, MediaQueryList> = {};
    const handlers: Record<string, (e: MediaQueryListEvent) => void> = {};

    Object.entries(mediaQueries).forEach(([key, query]) => {
      try {
        const mql = window.matchMedia(query);
        mediaQueryLists[key] = mql;

        const handler = (e: MediaQueryListEvent) => {
          setMatches(prev => ({ ...prev, [key]: e.matches }));
        };
        handlers[key] = handler;

        // Set initial value
        setMatches(prev => ({ ...prev, [key]: mql.matches }));

        // Add listener
        if (mql.addEventListener) {
          mql.addEventListener('change', handler);
        } else {
          mql.addListener(handler);
        }
      } catch (error) {
        console.warn(`Failed to set up media query for ${key}:`, error);
      }
    });

    return () => {
      Object.entries(handlers).forEach(([key, handler]) => {
        const mql = mediaQueryLists[key];
        if (mql) {
          if (mql.removeEventListener) {
            mql.removeEventListener('change', handler);
          } else {
            mql.removeListener(handler);
          }
        }
      });
    };
  }, []);

  return {
    effectiveTheme,
    ...matches,
  };
};