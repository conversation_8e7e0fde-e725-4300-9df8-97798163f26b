// Vercel Serverless Function for Admin Inquiry Management
import { getInquiries, getInquiryById, updateInquiry, getInquiryStats } from '../../src/lib/inquiryService.js';
import { validateInquiryFilters } from '../../src/lib/validation.js';
import { checkRateLimit } from '../../src/lib/rateLimiter.js';

/**
 * Get client IP address
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.headers['x-real-ip'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         'unknown';
}

/**
 * Simple admin authentication check
 * In production, this should be replaced with proper JWT or session-based auth
 */
function isAuthenticated(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7);
  // For now, use a simple token check
  // In production, implement proper JWT verification
  return token === process.env.ADMIN_API_TOKEN;
}

/**
 * Handle GET requests - fetch inquiries
 */
async function handleGetInquiries(req, res) {
  try {
    const { 
      status, 
      category, 
      urgencyLevel, 
      assignedTo, 
      dateFrom, 
      dateTo, 
      limit = '50', 
      offset = '0',
      stats = 'false'
    } = req.query;

    // If stats are requested, return statistics
    if (stats === 'true') {
      const statistics = getInquiryStats();
      return res.status(200).json({
        success: true,
        data: statistics
      });
    }

    // Prepare filters
    const filters = {
      status,
      category,
      urgencyLevel,
      assignedTo,
      dateFrom: dateFrom ? new Date(dateFrom) : undefined,
      dateTo: dateTo ? new Date(dateTo) : undefined,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    // Validate filters
    const validation = validateInquiryFilters(filters);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filters',
        details: validation.errors,
        code: 'VALIDATION_ERROR'
      });
    }

    // Get inquiries
    const inquiries = getInquiries(filters);

    // Return response
    res.status(200).json({
      success: true,
      data: inquiries,
      pagination: {
        limit: filters.limit,
        offset: filters.offset,
        total: inquiries.length
      }
    });

  } catch (error) {
    console.error('Get inquiries error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch inquiries',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
}

/**
 * Handle PUT requests - update inquiry
 */
async function handleUpdateInquiry(req, res) {
  try {
    const { id } = req.query;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        error: 'Inquiry ID is required',
        code: 'MISSING_ID'
      });
    }

    const { status, assignedTo, requiresProfessional } = req.body;

    // Validate update data
    const updateData = {};
    if (status !== undefined) {
      const validStatuses = ['new', 'in-progress', 'responded', 'closed', 'referred'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid status value',
          code: 'VALIDATION_ERROR'
        });
      }
      updateData.status = status;
    }

    if (assignedTo !== undefined) {
      if (typeof assignedTo !== 'string' || assignedTo.length > 100) {
        return res.status(400).json({
          success: false,
          error: 'Invalid assignedTo value',
          code: 'VALIDATION_ERROR'
        });
      }
      updateData.assignedTo = assignedTo.trim() || undefined;
    }

    if (requiresProfessional !== undefined) {
      if (typeof requiresProfessional !== 'boolean') {
        return res.status(400).json({
          success: false,
          error: 'requiresProfessional must be a boolean',
          code: 'VALIDATION_ERROR'
        });
      }
      updateData.requiresProfessional = requiresProfessional;
    }

    // Update inquiry
    const updatedInquiry = updateInquiry(id, updateData);

    if (!updatedInquiry) {
      return res.status(404).json({
        success: false,
        error: 'Inquiry not found',
        code: 'NOT_FOUND'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Inquiry updated successfully',
      data: updatedInquiry
    });

  } catch (error) {
    console.error('Update inquiry error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update inquiry',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Check authentication
  if (!isAuthenticated(req)) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized access',
      code: 'UNAUTHORIZED'
    });
  }

  // Rate limiting for admin endpoints - 100 requests per hour
  const clientIP = getClientIP(req);
  const rateLimitKey = `admin_inquiries_${clientIP}`;
  if (!checkRateLimit(rateLimitKey, 100, 60 * 60 * 1000)) {
    return res.status(429).json({
      success: false,
      error: 'Rate limit exceeded',
      code: 'RATE_LIMIT_EXCEEDED'
    });
  }

  // Route to appropriate handler
  switch (req.method) {
    case 'GET':
      return handleGetInquiries(req, res);
    case 'PUT':
      return handleUpdateInquiry(req, res);
    default:
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
        code: 'METHOD_NOT_ALLOWED'
      });
  }
}