import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight } from 'lucide-react';

interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, icon, link }) => {
  const { t } = useTranslation('common');
  return (
    <article 
      className="p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
      style={{
        backgroundColor: 'rgb(var(--color-surface))',
        borderColor: 'rgb(var(--color-border))'
      }}
    >
      <div 
        className="mb-4" 
        style={{ color: 'rgb(var(--color-secondary-500))' }}
        aria-hidden="true"
      >
        {icon}
      </div>
      <h3 
        className="text-xl font-bold mb-2"
        style={{ color: 'rgb(var(--color-text-primary))' }}
      >
        {title}
      </h3>
      <p 
        className="mb-4"
        style={{ color: 'rgb(var(--color-text-secondary))' }}
      >
        {description}
      </p>
      <Link 
        to={link} 
        className="inline-flex items-center font-medium transition-colors group focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md px-1 py-1"
        style={{ color: 'rgb(var(--color-secondary-500))' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = 'rgb(var(--color-primary-500))';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = 'rgb(var(--color-secondary-500))';
        }}
        aria-label={`Learn more about ${title}`}
      >
        {t('button_learnMore')}
        <ArrowRight 
          className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" 
          aria-hidden="true"
        />
      </Link>
    </article>
  );
};

export default ServiceCard;