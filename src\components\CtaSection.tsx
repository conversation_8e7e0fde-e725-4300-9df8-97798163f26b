import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface CtaSectionProps {
  title: string;
  description: string;
  ctaText: string;
  ctaLink: string;
}

const CtaSection: React.FC<CtaSectionProps> = ({ title, description, ctaText, ctaLink }) => {
  // Use the ctaLink as-is since it should already include language prefix
  const finalLink = ctaLink;

  return (
    <section className="py-16 bg-blue-800 dark:bg-gray-800 transition-colors duration-300">
      <div className="container mx-auto px-4 md:px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{title}</h2>
        <p className="text-xl text-blue-100 max-w-2xl mx-auto mb-8">{description}</p>
        <Link to={finalLink} className="btn-primary-lg inline-block">
          {ctaText}
        </Link>
      </div>
    </section>
  );
};

export default CtaSection;