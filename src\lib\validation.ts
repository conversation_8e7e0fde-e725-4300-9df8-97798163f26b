import type { InquiryCategory, UrgencyLevel } from '../types/database.js';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Email validation regex
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Valid inquiry categories
 */
const VALID_CATEGORIES: InquiryCategory[] = [
  'risk-assessment',
  'training',
  'compliance',
  'emergency',
  'general'
];

/**
 * Valid urgency levels
 */
const VALID_URGENCY_LEVELS: UrgencyLevel[] = [
  'low',
  'medium',
  'high',
  'emergency'
];

/**
 * Sanitize string input
 */
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  return EMAIL_REGEX.test(email) && email.length <= 254;
}

/**
 * Validate inquiry category
 */
export function isValidCategory(category: string): category is InquiryCategory {
  return VALID_CATEGORIES.includes(category as InquiryCategory);
}

/**
 * Validate urgency level
 */
export function isValidUrgencyLevel(level: string): level is UrgencyLevel {
  return VALID_URGENCY_LEVELS.includes(level as UrgencyLevel);
}

/**
 * Validate inquiry submission data
 */
export function validateInquiryData(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate contact name
  if (!data.contactName || typeof data.contactName !== 'string') {
    errors.push({ field: 'contactName', message: 'Contact name is required' });
  } else if (data.contactName.trim().length < 2) {
    errors.push({ field: 'contactName', message: 'Contact name must be at least 2 characters' });
  } else if (data.contactName.trim().length > 100) {
    errors.push({ field: 'contactName', message: 'Contact name must be less than 100 characters' });
  }

  // Validate contact email
  if (!data.contactEmail || typeof data.contactEmail !== 'string') {
    errors.push({ field: 'contactEmail', message: 'Contact email is required' });
  } else if (!isValidEmail(data.contactEmail.trim())) {
    errors.push({ field: 'contactEmail', message: 'Please provide a valid email address' });
  }

  // Validate company name (optional)
  if (data.companyName && typeof data.companyName === 'string') {
    if (data.companyName.trim().length > 200) {
      errors.push({ field: 'companyName', message: 'Company name must be less than 200 characters' });
    }
  }

  // Validate inquiry category
  if (!data.inquiryCategory || typeof data.inquiryCategory !== 'string') {
    errors.push({ field: 'inquiryCategory', message: 'Inquiry category is required' });
  } else if (!isValidCategory(data.inquiryCategory)) {
    errors.push({ field: 'inquiryCategory', message: 'Invalid inquiry category' });
  }

  // Validate inquiry subcategory (optional)
  if (data.inquirySubcategory && typeof data.inquirySubcategory === 'string') {
    if (data.inquirySubcategory.trim().length > 100) {
      errors.push({ field: 'inquirySubcategory', message: 'Inquiry subcategory must be less than 100 characters' });
    }
  }

  // Validate urgency level
  if (!data.urgencyLevel || typeof data.urgencyLevel !== 'string') {
    errors.push({ field: 'urgencyLevel', message: 'Urgency level is required' });
  } else if (!isValidUrgencyLevel(data.urgencyLevel)) {
    errors.push({ field: 'urgencyLevel', message: 'Invalid urgency level' });
  }

  // Validate description
  if (!data.description || typeof data.description !== 'string') {
    errors.push({ field: 'description', message: 'Description is required' });
  } else if (data.description.trim().length < 10) {
    errors.push({ field: 'description', message: 'Description must be at least 10 characters' });
  } else if (data.description.trim().length > 2000) {
    errors.push({ field: 'description', message: 'Description must be less than 2000 characters' });
  }

  // Validate requiresProfessional (optional boolean)
  if (data.requiresProfessional !== undefined && typeof data.requiresProfessional !== 'boolean') {
    errors.push({ field: 'requiresProfessional', message: 'requiresProfessional must be a boolean' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate admin inquiry filters
 */
export function validateInquiryFilters(filters: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate status filter
  if (filters.status && typeof filters.status === 'string') {
    const validStatuses = ['new', 'in-progress', 'responded', 'closed', 'referred'];
    if (!validStatuses.includes(filters.status)) {
      errors.push({ field: 'status', message: 'Invalid status filter' });
    }
  }

  // Validate category filter
  if (filters.category && typeof filters.category === 'string') {
    if (!isValidCategory(filters.category)) {
      errors.push({ field: 'category', message: 'Invalid category filter' });
    }
  }

  // Validate urgency level filter
  if (filters.urgencyLevel && typeof filters.urgencyLevel === 'string') {
    if (!isValidUrgencyLevel(filters.urgencyLevel)) {
      errors.push({ field: 'urgencyLevel', message: 'Invalid urgency level filter' });
    }
  }

  // Validate date filters
  if (filters.dateFrom) {
    const dateFrom = new Date(filters.dateFrom);
    if (isNaN(dateFrom.getTime())) {
      errors.push({ field: 'dateFrom', message: 'Invalid dateFrom format' });
    }
  }

  if (filters.dateTo) {
    const dateTo = new Date(filters.dateTo);
    if (isNaN(dateTo.getTime())) {
      errors.push({ field: 'dateTo', message: 'Invalid dateTo format' });
    }
  }

  // Validate pagination
  if (filters.limit !== undefined) {
    const limit = parseInt(filters.limit);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      errors.push({ field: 'limit', message: 'Limit must be between 1 and 100' });
    }
  }

  if (filters.offset !== undefined) {
    const offset = parseInt(filters.offset);
    if (isNaN(offset) || offset < 0) {
      errors.push({ field: 'offset', message: 'Offset must be a non-negative number' });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}