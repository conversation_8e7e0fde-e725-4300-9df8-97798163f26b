// Vercel Serverless Function for User Inquiry Submission
import { createInquiry } from '../../src/lib/inquiryService.js';
import { validateInquiryData, sanitizeString } from '../../src/lib/validation.js';
import { sendInquiryNotification, sendInquiryConfirmation } from '../../src/lib/emailService.js';
import { checkRateLimit } from '../../src/lib/rateLimiter.js';

/**
 * Get client IP address
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.headers['x-real-ip'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         'unknown';
}

/**
 * Log user activity for analytics
 */
async function logUserActivity(inquiryId, clientIP, userAgent) {
  try {
    // Import dynamically to avoid issues with database initialization
    const { getDatabase, generateId, userActivityToRow } = await import('../../src/lib/database.js');
    
    const db = getDatabase();
    const activity = {
      id: generateId(),
      sessionId: undefined,
      userEmail: undefined,
      ipAddress: clientIP,
      activityType: 'inquiry_submit',
      resourceId: undefined,
      inquiryId: inquiryId,
      metadata: JSON.stringify({ userAgent }),
      timestamp: new Date()
    };

    const row = userActivityToRow(activity);
    const stmt = db.prepare(`
      INSERT INTO user_activities (
        id, session_id, user_email, ip_address, activity_type,
        resource_id, inquiry_id, metadata, timestamp
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `);
    
    stmt.run(
      row.id,
      row.session_id,
      row.user_email,
      row.ip_address,
      row.activity_type,
      row.resource_id,
      row.inquiry_id,
      row.metadata
    );
  } catch (error) {
    console.error('Failed to log user activity:', error);
    // Don't fail the request if logging fails
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed' 
    });
  }

  try {
    const clientIP = getClientIP(req);
    const userAgent = req.headers['user-agent'] || 'Unknown';

    // Rate limiting - 5 inquiries per hour per IP
    const rateLimitKey = `inquiry_${clientIP}`;
    if (!checkRateLimit(rateLimitKey, 5, 60 * 60 * 1000)) {
      return res.status(429).json({
        success: false,
        error: 'Too many inquiries submitted. Please try again later.',
        code: 'RATE_LIMIT_EXCEEDED'
      });
    }

    // Validate request body
    const validation = validateInquiryData(req.body);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validation.errors,
        code: 'VALIDATION_ERROR'
      });
    }

    // Sanitize input data
    const sanitizedData = {
      contactName: sanitizeString(req.body.contactName),
      contactEmail: sanitizeString(req.body.contactEmail.toLowerCase()),
      companyName: req.body.companyName ? sanitizeString(req.body.companyName) : undefined,
      inquiryCategory: req.body.inquiryCategory,
      inquirySubcategory: req.body.inquirySubcategory ? sanitizeString(req.body.inquirySubcategory) : undefined,
      urgencyLevel: req.body.urgencyLevel,
      description: sanitizeString(req.body.description),
      requiresProfessional: Boolean(req.body.requiresProfessional)
    };

    // Create inquiry in database
    const inquiry = createInquiry(sanitizedData);

    // Log user activity
    await logUserActivity(inquiry.id, clientIP, userAgent);

    // Send email notifications
    try {
      // Send notification to admin
      await sendInquiryNotification(inquiry, clientIP);
      
      // Send confirmation to user
      const language = req.body.language === 'sq' ? 'sq' : 'en';
      await sendInquiryConfirmation(inquiry, language);
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
      // Don't fail the request if email fails - inquiry is already saved
    }

    // Return success response
    res.status(201).json({
      success: true,
      message: req.body.language === 'sq' 
        ? 'Kërkesa juaj u dërgua me sukses! Do t\'ju kontaktojmë së shpejti.'
        : 'Your inquiry has been submitted successfully! We will contact you soon.',
      data: {
        id: inquiry.id,
        status: inquiry.status,
        createdAt: inquiry.createdAt.toISOString(),
        category: inquiry.inquiryCategory,
        urgencyLevel: inquiry.urgencyLevel
      }
    });

  } catch (error) {
    console.error('Inquiry submission error:', error);
    
    // Return appropriate error response
    if (error.message.includes('UNIQUE constraint failed')) {
      return res.status(409).json({
        success: false,
        error: 'A similar inquiry already exists',
        code: 'DUPLICATE_INQUIRY'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred. Please try again later.',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
}